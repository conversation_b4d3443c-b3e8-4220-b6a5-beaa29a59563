<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Item History - {{ item.namaitem }}</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Add Toastify CSS and JS for notifications -->
    <link
      rel="stylesheet"
      type="text/css"
      href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css"
    />
    <script
      type="text/javascript"
      src="https://cdn.jsdelivr.net/npm/toastify-js"
    ></script>
    <!-- Add today-sales-details.js for Send to Telegram functionality -->
    <script src="/static/js/today-sales-details.js"></script>
    <script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            backgroundColor: {
              "gray-750": "#2d3748",
            },
          },
        },
      };
    </script>
  </head>
  <body class="bg-black text-gray-100">
    <div class="min-h-screen">
      <!-- Header - Made sticky -->
      <nav class="bg-gray-800 shadow-lg sticky top-0 z-50">
        <div class="max-w-[95%] mx-auto px-4">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <h1 class="text-3xl font-bold text-white">Item History</h1>
            </div>
            <div class="flex items-center">
              <!-- Chat History Button -->
              <button
                id="openChatHistoryBtn"
                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center mr-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"
                  />
                  <path
                    d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"
                  />
                </svg>
                Chat History
              </button>
              <!-- Edit Button -->
              <button
                onclick="window.open(`/edit-item/${encodeURIComponent('{{ item.kodeitem }}')}`, '_blank')"
                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center mr-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"
                  />
                </svg>
                Edit
              </button>
              <!-- Close Button -->
              <button
                onclick="window.close()"
                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  />
                </svg>
                Close
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Item Details -->
      <div class="max-w-[95%] mx-auto px-4 py-4">
        <div class="bg-gray-800 rounded-lg p-4 mb-4">
          <h2 class="text-xl font-bold mb-2">
            {{ item.namaitem }} - {{ item.keterangan or '' }}
          </h2>
          <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div>
              <p class="text-gray-400">Code</p>
              <p class="text-lg">{{ item.kodeitem }}</p>
            </div>
            <div>
              <p class="text-gray-400">Current Stock</p>
              <p
                onclick="openStockModal({{ current_stock }}, '{{ item.kodeitem }}', '{{ item.satuan }}')"
                class="cursor-pointer bg-green-800 text-white px-3 py-1 rounded-md inline-block {% if current_stock <= item.stokmin %}text-red-400{% endif %}"
              >
                {{ "{:.2f}".format(current_stock).rstrip('0').rstrip('.') }}
              </p>
            </div>
            <div>
              <p class="text-gray-400">Min Stock</p>
              <p
                class="bg-blue-800 text-white px-3 py-1 rounded-md inline-block"
              >
                {{ "{:.2f}".format(item.stokmin).rstrip('0').rstrip('.') }}
              </p>
            </div>
            <div>
              <p class="text-gray-400">Location</p>
              <p class="text-lg">{{ item.rak }}</p>
            </div>
            <div>
              <p class="text-gray-400">Type</p>
              <p class="text-lg">{{ item.jenis }}</p>
            </div>
          </div>
        </div>
      </div>
      <!-- Monthly Sales Summary -->
      <div class="max-w-[95%] mx-auto px-4 py-4">
        <div class="bg-gray-800 rounded-lg p-4 mb-4">
          <h2 class="text-xl font-bold mb-2">Monthly Sales Summary</h2>
          <div class="grid grid-cols-6 gap-2">
            {% for month in monthly_sales %}
            <div class="bg-gray-750 rounded-lg p-2 text-center">
              <p class="text-sm font-medium">
                {{ month.month_name[:3] }} {{ month.year }}
              </p>
              <p class="text-green-400 font-bold">{{ month.units }}</p>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>

      <!-- History Table -->
      <div class="overflow-x-auto bg-gray-800 rounded-lg shadow mx-4">
        <table class="w-full divide-y divide-gray-700">
          <thead class="bg-gray-700">
            <tr>
              <th
                scope="col"
                class="px-4 py-3 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
              >
                Date
              </th>
              <th
                scope="col"
                class="px-4 py-3 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
              >
                Stock
              </th>
              <th
                scope="col"
                class="px-4 py-3 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
              >
                Transaction
              </th>
              <th
                scope="col"
                class="px-4 py-3 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
              >
                Unit
              </th>
              <th
                scope="col"
                class="px-4 py-3 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
              >
                Price
              </th>
              <th
                scope="col"
                class="px-4 py-3 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
              >
                Total
              </th>
              <th
                scope="col"
                class="px-4 py-3 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
              >
                Keterangan
              </th>
              <th
                scope="col"
                class="px-4 py-3 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
              >
                Source
              </th>
            </tr>
          </thead>
          <tbody class="bg-gray-800 divide-y divide-gray-700">
            {% for record in history[:300] %} {% set is_last_week =
            (now.replace(tzinfo=None) - record.tanggal).days <= 7 and
            (now.replace(tzinfo=None) - record.tanggal).days > 0 %}
            <tr
              class="hover:bg-gray-700 transition duration-150 {% if is_last_week %}bg-yellow-800/50{% else %}bg-black{% endif %}"
            >
              <td
                class="px-4 py-3 whitespace-nowrap text-base {% if record.tipe == 'BL' %}text-green-400{% else %}text-gray-300{% endif %}"
              >
                {% set amount = "%.1f"|format(record.jumlah) %} {{
                amount|replace('.0', '') }} - {{ record.tanggal.strftime('%a, %d
                %b %Y') }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-base text-gray-300">
                {% set stock = "{:.2f}".format(record.running_stock) %} {{
                stock.rstrip('0').rstrip('.') }}
              </td>
              <td
                class="px-4 py-3 whitespace-nowrap text-base {% if record.tipe in ['BL', 'RJ', 'IM'] %}text-green-400{% elif record.tipe == 'IK' %}text-fuchsia-400{% elif record.tipe == 'OP' %}text-yellow-400{% else %}text-gray-300{% endif %}"
              >
                <span
                  class="cursor-pointer hover:underline"
                  onclick="showTransactionDetails('{{ record.notransaksi }}')"
                >
                  {{ record.notransaksi }}
                </span>
                {% if record.tipe == 'IM' %} (Masuk) {% elif
                (record.supplier_name and record.tipe != 'KSR') or record.tipe
                == 'IK' %} {% if record.supplier_name and
                record.supplier_name not in ['UMUM', 'None', None] %}-{{
                record.supplier_name }}-{% endif %} {% if record.tipe == 'BL'
                %}(Beli) {% elif record.tipe == 'JL' %}(Jual) {% elif
                record.tipe == 'IK' %}(Keluar) {% elif record.tipe == 'RJ'
                %}(Kembali) {% else %}({{ record.tipe }}){% endif %} {% endif %}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-base text-gray-300">
                {{ record.satuan }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-base text-gray-300">
                {{ "{:,.0f}".format(record.harga) }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-base text-gray-300">
                {{ "{:,.0f}".format(record.total) }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-base text-gray-300">
                {{ record.keterangan or '' }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-base {% if record.db_source == 'old' %}text-amber-400{% else %}text-blue-400{% endif %}">
                {{ record.db_source }}
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
        {% if history|length > 300 %}
        <div class="text-gray-400 text-sm p-4 text-center">
          Showing the most recent 300 records out of {{ history|length }} total
          records
        </div>
        {% endif %}
      </div>
    </div>
    <script>
      // Add Chat History functionality
      document.addEventListener("DOMContentLoaded", function () {
        const openChatHistoryBtn =
          document.getElementById("openChatHistoryBtn");
        if (openChatHistoryBtn) {
          openChatHistoryBtn.addEventListener("click", function () {
            // Decode HTML entities in the item name
            const itemNameElement = document.createElement("textarea");
            itemNameElement.innerHTML = "{{ item.namaitem }}";
            const decodedItemName = itemNameElement.value;

            const itemCode = "{{ item.kodeitem }}";

            // Call the searchChatHistory function from today-sales-details.js
            if (typeof searchChatHistory === "function") {
              searchChatHistory(decodedItemName, itemCode);
            } else {
              console.error("searchChatHistory function not found");
              Toastify({
                text: "Chat history functionality not available",
                duration: 3000,
                gravity: "top",
                position: "center",
                style: {
                  background: "#ef4444", // red-600
                },
              }).showToast();
            }
          });
        }

        // Fallback copy method using execCommand
        function fallbackCopy(text) {
          try {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed"; // Avoid scrolling to bottom
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.width = "2em";
            textArea.style.height = "2em";
            textArea.style.padding = "0";
            textArea.style.border = "none";
            textArea.style.outline = "none";
            textArea.style.boxShadow = "none";
            textArea.style.background = "transparent";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            const successful = document.execCommand("copy");
            document.body.removeChild(textArea);

            if (successful) {
              // Show success notification
              Toastify({
                text: "Copied to clipboard",
                duration: 1500,
                gravity: "bottom",
                position: "center",
                style: {
                  background: "#3b82f6", // blue-500
                  borderRadius: "8px",
                  fontSize: "14px",
                  padding: "8px 16px",
                },
              }).showToast();
            } else {
              console.error("Fallback copy failed");
              // Show error notification
              Toastify({
                text: "Failed to copy text",
                duration: 1500,
                gravity: "bottom",
                position: "center",
                style: {
                  background: "#ef4444", // red-600
                  borderRadius: "8px",
                  fontSize: "14px",
                  padding: "8px 16px",
                },
              }).showToast();
            }
          } catch (err) {
            console.error("Fallback copy error: ", err);
            // Show error notification
            Toastify({
              text: "Failed to copy text",
              duration: 1500,
              gravity: "bottom",
              position: "center",
              style: {
                background: "#ef4444", // red-600
                borderRadius: "8px",
                fontSize: "14px",
                padding: "8px 16px",
              },
            }).showToast();
          }
        }

        // Add copy on select functionality directly to the document
        document.addEventListener("mouseup", function () {
          const selectedText = window.getSelection().toString().trim();
          if (selectedText) {
            // Try modern Clipboard API first
            if (navigator.clipboard && navigator.clipboard.writeText) {
              navigator.clipboard
                .writeText(selectedText)
                .then(() => {
                  // Show a notification when text is copied
                  Toastify({
                    text: "Copied to clipboard",
                    duration: 1500,
                    gravity: "bottom",
                    position: "center",
                    style: {
                      background: "#3b82f6", // blue-500
                      borderRadius: "8px",
                      fontSize: "14px",
                      padding: "8px 16px",
                    },
                  }).showToast();
                })
                .catch((err) => {
                  console.error("Failed to copy text: ", err);
                  // Try fallback method
                  fallbackCopy(selectedText);
                });
            } else {
              // Fall back to older method
              fallbackCopy(selectedText);
            }
          }
        });
      });
    </script>
    <!-- Stock Update Modal -->
    <div
      id="stockModal"
      class="fixed inset-0 bg-gray-900 bg-opacity-70 hidden z-50"
      onclick="closeStockModal()"
    >
      <div class="flex items-center justify-center min-h-screen p-4">
        <div
          class="bg-gray-750 p-6 rounded-lg shadow-xl border-2 border-blue-500 w-full max-w-md relative"
          onclick="event.stopPropagation()"
        >
          <h3 class="text-xl font-bold text-white mb-4">Update Stock</h3>
          <div class="mb-4">
            <label class="block text-gray-400 mb-2"
              >Current Stock: <span id="currentStockValue"></span
            ></label>
            <input
              type="number"
              id="newStockInput"
              step="0.01"
              class="w-full bg-gray-700 text-white px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div class="flex justify-end space-x-2">
            <button
              type="button"
              onclick="event.stopPropagation(); closeStockModal()"
              class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              type="button"
              onclick="event.stopPropagation(); updateStock()"
              class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Update
            </button>
          </div>
        </div>
      </div>
    </div>
    <script>
      let currentItemCode = "";
      let currentItemUnit = "";

      function openStockModal(currentStock, itemCode, itemUnit) {
        console.log("Opening stock modal:", currentStock, itemCode, itemUnit);
        currentItemCode = itemCode;
        currentItemUnit = itemUnit;
        document.getElementById("stockModal").classList.remove("hidden");
        document.getElementById("currentStockValue").textContent = currentStock;
        const input = document.getElementById("newStockInput");
        input.value = currentStock;
        input.focus();
        input.select();
      }

      function closeStockModal() {
        document.getElementById("stockModal").classList.add("hidden");
      }

      async function updateStock() {
        const newStock = parseFloat(
          document.getElementById("newStockInput").value
        );
        if (isNaN(newStock)) {
          alert("Please enter a valid number");
          return;
        }

        try {
          const response = await fetch("/api/update-stock", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              kodeitem: currentItemCode,
              new_stock: newStock,
              satuan: currentItemUnit,
            }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || "Failed to update stock");
          }

          const data = await response.json();
          closeStockModal();
          alert("Stock updated successfully");
          location.reload();
        } catch (error) {
          alert(error.message || "Failed to update stock");
        }
      }
    </script>
    <!-- Transaction Modal -->
    <div
      id="transactionModal"
      class="fixed inset-0 bg-gray-900 bg-opacity-70 hidden z-50"
      onclick="closeTransactionModal()"
    >
      <div class="flex items-center justify-center min-h-screen p-4">
        <div
          class="bg-gray-750 p-6 rounded-lg shadow-xl border-2 border-blue-500 w-full max-w-4xl max-h-[90vh] overflow-y-auto"
          onclick="event.stopPropagation()"
        >
          <div class="flex justify-between items-center mb-4">
            <div>
              <h3 class="text-xl font-bold text-white">Transaction Details</h3>
              <div class="text-gray-400" id="transactionInfo"></div>
              <div class="text-sm mt-1">
                <span class="text-gray-400">Source: </span>
                <span id="transactionSource" class="font-medium"></span>
              </div>
            </div>
            <button
              onclick="event.stopPropagation(); closeTransactionModal()"
              class="text-gray-400 hover:text-white"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-700">
              <thead class="bg-gray-700">
                <tr>
                  <th
                    scope="col"
                    class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                  >
                    Item Code
                  </th>
                  <th
                    scope="col"
                    class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                  >
                    Item Name
                  </th>
                  <th
                    scope="col"
                    class="px-4 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider"
                  >
                    Quantity
                  </th>
                  <th
                    scope="col"
                    class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                  >
                    Unit
                  </th>
                  <th
                    scope="col"
                    class="px-4 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider"
                  >
                    Price
                  </th>
                  <th
                    scope="col"
                    class="px-4 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider"
                  >
                    Discount
                  </th>
                  <th
                    scope="col"
                    class="px-4 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider"
                  >
                    Total
                  </th>
                  <th
                    scope="col"
                    class="px-4 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider"
                  >
                    Source
                  </th>
                </tr>
              </thead>
              <tbody
                id="transactionDetails"
                class="bg-gray-800 divide-y divide-gray-700"
              ></tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <script>
      function getTransactionType(type) {
        const types = {
          BL: "Purchase",
          JL: "Sale",
          RJ: "Return",
          IM: "Stock In",
          IK: "Stock Out",
          OP: "Stock Opname",
          KSR: "Cashier",
        };
        return types[type] || type;
      }

      async function showTransactionDetails(notransaksi) {
        console.log("Showing transaction details for:", notransaksi);
        try {
          // Encode transaction ID to handle special characters
          const encodedNotransaksi = encodeURIComponent(notransaksi);
          console.log("Encoded transaction ID:", encodedNotransaksi);

          // Make API request
          const response = await fetch(
            `/api/transaction-details/${encodedNotransaksi}`
          );
          console.log("API response status:", response.status);

          if (!response.ok) {
            const errorData = await response.json();
            console.error("Error response:", errorData);
            throw new Error(
              errorData.detail || "Failed to fetch transaction details"
            );
          }

          const data = await response.json();
          console.log("Transaction data:", data);

          // Calculate subtotal
          let subtotal = 0;
          data.details.forEach((detail) => {
            subtotal += detail.total;
          });

          // Update transaction info
          const date = new Date(data.header.tanggal).toLocaleDateString(
            "en-US",
            {
              weekday: "short",
              year: "numeric",
              month: "short",
              day: "numeric",
            }
          );

          document.getElementById(
            "transactionInfo"
          ).textContent = `${notransaksi} - ${date} - ${getTransactionType(
            data.header.tipe
          )}`;
          
          // Update source information
          const sourceElement = document.getElementById("transactionSource");
          sourceElement.textContent = data.header.db_source || "unknown";
          sourceElement.className = data.header.db_source === "old" ? "font-medium text-yellow-500" : "font-medium text-green-500";

          // Clear previous details
          const tbody = document.getElementById("transactionDetails");
          tbody.innerHTML = "";

          // Populate details table
          data.details.forEach((detail) => {
            const row = document.createElement("tr");
            row.className = "hover:bg-gray-700";
            
            // Determine source color
            const sourceColor = detail.db_source === "old" ? "text-yellow-500" : "text-green-500";
            
            row.innerHTML = `
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">${
                          detail.kodeitem
                        }</td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">${
                          detail.namaitem
                        }</td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300 text-right">${
                          detail.jumlah
                        }</td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">${
                          detail.satuan
                        }</td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300 text-right">${detail.harga.toLocaleString()}</td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm ${
                          detail.potongan > 0 ? "text-red-400" : "text-gray-300"
                        } text-right">${detail.potongan.toLocaleString()}</td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300 text-right">${detail.total.toLocaleString()}</td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm ${sourceColor} text-center">${detail.db_source || "unknown"}</td>
                    `;
            tbody.appendChild(row);
          });

          // Add summary section
          const existingSummary = document.getElementById("transactionSummary");
          if (existingSummary) {
            existingSummary.remove();
          }

          const summaryDiv = document.createElement("div");
          summaryDiv.id = "transactionSummary";
          summaryDiv.className = "mt-4 flex justify-end";

          const discountAmount = data.header.potnomfaktur || 0;
          const finalTotal = subtotal - discountAmount;

          summaryDiv.innerHTML = `
                    <table class="text-right">
                        <tr>
                            <td class="pr-4 text-gray-400">Subtotal:</td>
                            <td class="text-gray-300">${subtotal.toLocaleString()}</td>
                        </tr>
                        <tr>
                            <td class="pr-4 text-gray-400">Discount:</td>
                            <td class="${
                              discountAmount > 0
                                ? "text-red-400"
                                : "text-gray-300"
                            }">${
            discountAmount > 0 ? "-" + discountAmount.toLocaleString() : "0"
          }</td>
                        </tr>
                        <tr class="font-bold">
                            <td class="pr-4 text-gray-400">Total:</td>
                            <td class="text-gray-300">${finalTotal.toLocaleString()}</td>
                        </tr>
                    </table>
                `;

          // Append summary to modal
          const modalContent = document.querySelector(
            "#transactionModal .bg-gray-750"
          );
          modalContent.appendChild(summaryDiv);

          // Show modal
          document
            .getElementById("transactionModal")
            .classList.remove("hidden");
        } catch (error) {
          console.error("Error in showTransactionDetails:", error);
          alert(error.message || "Failed to load transaction details");
        }
      }

      function closeTransactionModal() {
        const modal = document.getElementById("transactionModal");
        modal.classList.add("hidden");
      }

      // Add event listener for Enter key on stock input
      document.addEventListener("DOMContentLoaded", function () {
        const newStockInput = document.getElementById("newStockInput");
        if (newStockInput) {
          newStockInput.addEventListener("keypress", function (e) {
            if (e.key === "Enter") {
              e.preventDefault();
              updateStock();
            }
          });
        }

        // Add click event listener to close modal when clicking outside
        const modal = document.getElementById("transactionModal");
        if (modal) {
          modal.addEventListener("click", function (e) {
            if (e.target === this) {
              closeTransactionModal();
            }
          });
        }

        const stockModal = document.getElementById("stockModal");
        if (stockModal) {
          stockModal.addEventListener("click", function (e) {
            if (e.target === this) {
              closeStockModal();
            }
          });
        }
      });
    </script>
  </body>
</html>
