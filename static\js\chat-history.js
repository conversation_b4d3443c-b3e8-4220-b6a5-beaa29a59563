// Chat History JavaScript Functions
// Refactored from inline scripts in chat_history.html

// Global variables
let hideBoughtItems = false;
let showNoBuyOnly = false;
let sortDirection = {};

// Initialize all functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeItemCheckboxes();
    initializeToggleButtons();
    initializeSaveButton();
    initializeUpdateTelegramButton();
    initializeSearchFunctionality();
    initializeExportButton();
    initializeClipboardImport();
    colorDateCells();
});

// Handle individual checkbox changes
function handleCheckboxChange(checkbox) {
    const row = checkbox.closest('tr');
    const itemNameCell = row.querySelector('td:nth-child(3)');
    const itemNameDiv = itemNameCell.querySelector('div');
    const itemNameLink = itemNameCell.querySelector('a');
    
    if (checkbox.checked) {
        // Mark as bought - red color and strikethrough
        row.classList.add('bought-item');
        if (itemNameDiv) {
            itemNameDiv.classList.add('line-through', 'text-red-500');
        }
        if (itemNameLink) {
            itemNameLink.classList.remove('text-blue-400', 'hover:text-blue-300');
            itemNameLink.classList.add('text-red-500');
        }
    } else {
        // Mark as not bought - restore original styling
        row.classList.remove('bought-item');
        if (itemNameDiv) {
            itemNameDiv.classList.remove('line-through', 'text-red-500');
        }
        if (itemNameLink) {
            itemNameLink.classList.remove('text-red-500');
            itemNameLink.classList.add('text-blue-400', 'hover:text-blue-300');
        }
    }
    updateItemVisibility();
}

// Initialize item checkboxes
function initializeItemCheckboxes() {
    const selectAllCheckbox = document.getElementById('selectAll');
    
    // Add event listeners to all item checkboxes
    document.querySelectorAll('.item-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            handleCheckboxChange(this);
        });
    });

    // Handle select all checkbox
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const itemCheckboxes = document.querySelectorAll('.item-checkbox');
            itemCheckboxes.forEach(checkbox => {
                if (checkbox.checked !== this.checked) {
                    checkbox.checked = this.checked;
                    handleCheckboxChange(checkbox);
                }
            });
        });
    }
}

// Update item visibility based on filters and search
function updateItemVisibility() {
    const allRows = document.querySelectorAll('tbody tr.item-row');
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    let visibleCount = 0;
    
    allRows.forEach(row => {
        const isBoughtItem = row.classList.contains('bought-item');
        const isNoBuyItem = row.classList.contains('no-buy-item');
        let shouldShow = true;

        // Apply bought items filter
        if (hideBoughtItems && isBoughtItem) {
            shouldShow = false;
        }

        // Apply 'No BUY' filter
        if (showNoBuyOnly && !isNoBuyItem) {
            shouldShow = false;
        }

        // Apply search filter
        if (shouldShow && searchTerm) {
            const itemNameCell = row.querySelector('td:nth-child(3)'); // Item Name column
            const jenisCell = row.querySelector('td:nth-child(4)'); // Jenis column
            
            let itemNameText = '';
            let jenisText = '';
            
            // Extract item name text
            if (itemNameCell) {
                const link = itemNameCell.querySelector('a');
                const span = itemNameCell.querySelector('span');
                itemNameText = (link ? link.textContent : (span ? span.textContent : itemNameCell.textContent)).toLowerCase();
            }
            
            // Extract jenis text
            if (jenisCell) {
                const jenisSpan = jenisCell.querySelector('span');
                jenisText = jenisSpan ? jenisSpan.textContent.toLowerCase() : '';
            }
            
            // Multi-field keyword search - all keywords must match in either field
            const keywords = searchTerm.split(/\s+/).filter(keyword => keyword.length > 0);
            const matchesSearch = keywords.every(keyword => 
                itemNameText.includes(keyword) || jenisText.includes(keyword)
            );
            
            if (!matchesSearch) {
                shouldShow = false;
            }
        }

        row.style.display = shouldShow ? '' : 'none';
        if (shouldShow) visibleCount++;
    });
    
    // Update search results count
    const searchResults = document.getElementById('searchResults');
    if (searchResults) {
        if (searchTerm) {
            searchResults.textContent = `${visibleCount} items found`;
        } else {
            searchResults.textContent = '';
        }
    }
}

// Initialize toggle buttons
function initializeToggleButtons() {
    const toggleBoughtButton = document.getElementById('toggleBoughtItems');
    const toggleNoBuyButton = document.getElementById('toggleNoBuyItems');

    if (toggleBoughtButton) {
        toggleBoughtButton.addEventListener('click', function() {
            if (hideBoughtItems) {
                // Show bought items
                toggleBoughtButton.textContent = 'Hide Bought Items';
                toggleBoughtButton.classList.remove('bg-green-600', 'hover:bg-green-700');
                toggleBoughtButton.classList.add('bg-blue-600', 'hover:bg-blue-700');
            } else {
                // Hide bought items
                toggleBoughtButton.textContent = 'Show Bought Items';
                toggleBoughtButton.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                toggleBoughtButton.classList.add('bg-green-600', 'hover:bg-green-700');
            }
            
            hideBoughtItems = !hideBoughtItems;
            updateItemVisibility();
        });
    }

    if (toggleNoBuyButton) {
        toggleNoBuyButton.addEventListener('click', function() {
            if (showNoBuyOnly) {
                // Show all items (respect bought items filter)
                toggleNoBuyButton.textContent = "Show 'No BUY' Only";
                toggleNoBuyButton.classList.remove('bg-orange-600', 'hover:bg-orange-700');
                toggleNoBuyButton.classList.add('bg-purple-600', 'hover:bg-purple-700');
            } else {
                // Show only 'No BUY' items
                toggleNoBuyButton.textContent = "Show All Items";
                toggleNoBuyButton.classList.remove('bg-purple-600', 'hover:bg-purple-700');
                toggleNoBuyButton.classList.add('bg-orange-600', 'hover:bg-orange-700');
            }
            
            showNoBuyOnly = !showNoBuyOnly;
            updateItemVisibility();
        });
    }
}

// Initialize save button functionality
function initializeSaveButton() {
    const saveButton = document.getElementById('saveNonBoughtItems');
    if (!saveButton) return;

    saveButton.addEventListener('click', async function() {
        try {
            // Collect all non-bought items (rows without red color/strikethrough)
            const nonBoughtItems = [];
            const allRows = document.querySelectorAll('tbody tr.item-row');
            
            allRows.forEach(row => {
                if (!row.classList.contains('bought-item')) {
                    // Extract data from the row (skip checkbox column)
                    const cells = row.querySelectorAll('td');
                    if (cells.length >= 5) {
                        // Get the actual user field from the data, not the displayed text
                        const userCell = cells[4]; // Sender column
                        let userValue = 'Unknown';
                        
                        // Try to extract user from the row's data attributes or find it in the template data
                        const rowIndex = row.getAttribute('data-item-id');
                        if (rowIndex && window.chatData) {
                            const entryIndex = parseInt(rowIndex) - 1;
                            if (window.chatData && window.chatData[entryIndex]) {
                                const entry = window.chatData[entryIndex];
                                // Prioritize 'user' field as it's the standard format in chat_history.json
                                userValue = entry.user || entry.sender || entry.from || 'Unknown';
                            }
                        }
                        
                        const item = {
                            date: cells[1].textContent.trim(),
                            user: userValue,
                            message: cells[2].textContent.trim()
                        };
                        nonBoughtItems.push(item);
                    }
                }
            });

            if (nonBoughtItems.length === 0) {
                alert('No non-bought items to save.');
                return;
            }

            // Send to backend
            const response = await fetch('/save-non-bought-items', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(nonBoughtItems)
            });

            if (response.ok) {
                const result = await response.json();
                alert(`Successfully saved ${nonBoughtItems.length} non-bought items to ${result.filename}`);
            } else {
                const error = await response.json();
                alert(`Error saving items: ${error.detail}`);
            }
        } catch (error) {
            console.error('Error saving non-bought items:', error);
            alert('Error saving items. Please check the console for details.');
        }
    });
}

// Initialize update Telegram button
function initializeUpdateTelegramButton() {
    const updateTelegramBtn = document.getElementById('updateTelegramBtn');
    if (!updateTelegramBtn) return;

    updateTelegramBtn.addEventListener('click', async function() {
        try {
            // Show loading state
            updateTelegramBtn.disabled = true;
            updateTelegramBtn.innerHTML = '<span class="text-white">Updating...</span>';
            
            // Call the update endpoint
            const response = await fetch('/update-telegram-messages', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                const result = await response.json();
                alert(`Successfully updated Telegram messages! Found ${result.message_count} new messages.`);
                // Reload the page to show updated data
                window.location.reload();
            } else {
                const error = await response.json();
                alert(`Error updating Telegram messages: ${error.detail}`);
            }
        } catch (error) {
            console.error('Error updating Telegram messages:', error);
            alert('Error updating Telegram messages. Please check the console for details.');
        } finally {
            // Reset button state
            updateTelegramBtn.disabled = false;
            updateTelegramBtn.innerHTML = '<span class="text-white">Update Telegram</span>';
        }
    });
}

// Table sorting functionality
window.sortTable = function(columnIndex) {
    const table = document.querySelector('table tbody');
    const rows = Array.from(table.querySelectorAll('tr')).filter(row => !row.querySelector('td[colspan]'));
    
    // Initialize sort direction for this column if not set
    if (!sortDirection[columnIndex]) {
        sortDirection[columnIndex] = 'asc';
    } else {
        sortDirection[columnIndex] = sortDirection[columnIndex] === 'asc' ? 'desc' : 'asc';
    }
    
    // Update sort indicators
    document.querySelectorAll('.sort-indicator').forEach(indicator => {
        indicator.textContent = '↕';
    });
    
    const currentIndicator = document.querySelector(`th:nth-child(${columnIndex + 1}) .sort-indicator`);
    if (currentIndicator) {
        currentIndicator.textContent = sortDirection[columnIndex] === 'asc' ? '↑' : '↓';
    }
    
    // Sort rows
    rows.sort((a, b) => {
        let aValue = '';
        let bValue = '';
        
        const aCell = a.querySelector(`td:nth-child(${columnIndex + 1})`);
        const bCell = b.querySelector(`td:nth-child(${columnIndex + 1})`);
        
        if (columnIndex === 1) { // Date column
            aValue = aCell.textContent.trim();
            bValue = bCell.textContent.trim();
            // Convert to Date objects for proper sorting
            const aDate = new Date(aValue);
            const bDate = new Date(bValue);
            return sortDirection[columnIndex] === 'asc' ? aDate - bDate : bDate - aDate;
        } else if (columnIndex === 3) { // Jenis column
            const aSpan = aCell.querySelector('span');
            const bSpan = bCell.querySelector('span');
            aValue = aSpan ? aSpan.textContent.trim() : '';
            bValue = bSpan ? bSpan.textContent.trim() : '';
        } else if (columnIndex === 4) { // Stock column
            aValue = aCell.textContent.trim();
            bValue = bCell.textContent.trim();
            // Convert to numbers for proper sorting
            const aNum = aValue === '-' ? -1 : parseFloat(aValue);
            const bNum = bValue === '-' ? -1 : parseFloat(bValue);
            return sortDirection[columnIndex] === 'asc' ? aNum - bNum : bNum - aNum;
        } else if (columnIndex === 5) { // Buying Price column
            aValue = aCell.textContent.trim();
            bValue = bCell.textContent.trim();
            // Convert to numbers for proper sorting (remove commas first)
            const aNum = aValue === '-' ? -1 : parseFloat(aValue.replace(/,/g, ''));
            const bNum = bValue === '-' ? -1 : parseFloat(bValue.replace(/,/g, ''));
            return sortDirection[columnIndex] === 'asc' ? aNum - bNum : bNum - aNum;
        } else if (columnIndex === 7) { // Type column
            const aSpan = aCell.querySelector('span');
            const bSpan = bCell.querySelector('span');
            aValue = aSpan ? aSpan.textContent.trim() : '';
            bValue = bSpan ? bSpan.textContent.trim() : '';
        } else {
            aValue = aCell.textContent.trim();
            bValue = bCell.textContent.trim();
        }
        
        // String comparison
        if (sortDirection[columnIndex] === 'asc') {
            return aValue.localeCompare(bValue);
        } else {
            return bValue.localeCompare(aValue);
        }
    });
    
    // Re-append sorted rows
    rows.forEach(row => table.appendChild(row));
    
    // Re-apply date colors after sorting
    colorDateCells();
};

// Function to color date cells based on age
function colorDateCells() {
    const dateCells = document.querySelectorAll('.date-cell');
    const currentDate = new Date();
    
    dateCells.forEach(cell => {
        const dateStr = cell.getAttribute('data-date');
        if (dateStr && dateStr !== '') {
            const entryDate = new Date(dateStr);
            const daysDiff = Math.floor((currentDate - entryDate) / (1000 * 60 * 60 * 24));
            
            // Remove existing color classes
            cell.classList.remove('text-white', 'text-yellow-400', 'text-red-400', 'text-gray-300');
            
            if (daysDiff < 30) {
                cell.classList.add('text-white');
            } else if (daysDiff >= 30 && daysDiff <= 60) {
                cell.classList.add('text-yellow-400');
            } else {
                cell.classList.add('text-red-400');
            }
        } else {
            // For N/A dates, keep gray
            cell.classList.remove('text-white', 'text-yellow-400', 'text-red-400');
            cell.classList.add('text-gray-300');
        }
    });
}

// Initialize search functionality
function initializeSearchFunctionality() {
    const searchInput = document.getElementById('searchInput');
    const clearSearchButton = document.getElementById('clearSearch');
    
    if (!searchInput) return;

    // Real-time search as user types
    searchInput.addEventListener('input', function() {
        updateItemVisibility();
    });
    
    // Clear search functionality
    if (clearSearchButton) {
        clearSearchButton.addEventListener('click', function() {
            searchInput.value = '';
            updateItemVisibility();
            searchInput.focus();
        });
    }
    
    // Allow Enter key to trigger search (though it's already real-time)
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            updateItemVisibility();
        }
    });
    
    // Click outside search box to focus and select all text functionality
    document.addEventListener('click', function(event) {
        // Check if the click is outside the search input and its parent container
        const searchContainer = searchInput.closest('.relative');
        const isClickInsideSearch = searchContainer && searchContainer.contains(event.target);
        const isClickOnClearButton = clearSearchButton && clearSearchButton.contains(event.target);
        
        // Check if click is inside the clipboard modal
        const clipboardModal = document.getElementById('clipboardModal');
        const isClickInsideModal = clipboardModal && clipboardModal.contains(event.target);
        
        // If click is outside search area and not on clear button and not inside modal, focus and select all
        if (!isClickInsideSearch && !isClickOnClearButton && !isClickInsideModal) {
            searchInput.focus();
            searchInput.select();
        }
    });
}

// Initialize Excel export functionality
function initializeExportButton() {
    const exportToExcelButton = document.getElementById('exportToExcel');
    if (!exportToExcelButton) return;

    exportToExcelButton.addEventListener('click', async function() {
        // Capture original text before any operations
        const originalText = exportToExcelButton.textContent;
        
        try {
            // Show loading state
            exportToExcelButton.textContent = 'Exporting...';
            exportToExcelButton.disabled = true;
            
            // Collect only visible/filtered items
            const visibleItems = [];
            const allRows = document.querySelectorAll('tbody tr.item-row');
            
            allRows.forEach((row, index) => {
                // Only include rows that are currently visible
                if (row.style.display !== 'none') {
                    const rowIndex = row.getAttribute('data-item-id');
                    if (rowIndex && window.chatData) {
                        const entryIndex = parseInt(rowIndex) - 1;
                        if (window.chatData[entryIndex]) {
                            const entry = window.chatData[entryIndex];
                            visibleItems.push({
                                date: entry.date || '',
                                user: entry.user || entry.sender || entry.from || 'Unknown',
                                message: entry.message || entry.text || entry.content || '',
                                kodeitem: entry.kodeitem || '',
                                jenis: entry.jenis || '',
                                stock: entry.stock || '',
                                buying_price: entry.buying_price || '',
                                strikethrough: entry.strikethrough || false
                            });
                        }
                    }
                }
            });
            
            if (visibleItems.length === 0) {
                alert('No visible items to export.');
                return;
            }
            
            // Get current search text for filename
            const searchInput = document.getElementById('searchInput');
            const searchText = searchInput ? searchInput.value.trim() : '';
            
            // Send POST request to export endpoint
            const response = await fetch('/export-chat-history-excel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    items: visibleItems,
                    search_text: searchText
                })
            });
            
            if (response.ok) {
                const result = await response.json();
                alert(`Successfully exported ${result.count} items to ${result.path}`);
            } else {
                const error = await response.json();
                alert(`Error exporting to Excel: ${error.detail}`);
            }
        } catch (error) {
            console.error('Error exporting to Excel:', error);
            alert('Error exporting to Excel. Please check the console for details.');
        } finally {
            // Reset button state - this will always run
            exportToExcelButton.textContent = originalText;
            exportToExcelButton.disabled = false;
        }
    });
}

// Initialize clipboard import functionality
function initializeClipboardImport() {
    const importFromClipboardBtn = document.getElementById('importFromClipboard');
    const clipboardModal = document.getElementById('clipboardModal');
    const closeModal = document.getElementById('closeModal');
    const cancelImport = document.getElementById('cancelImport');
    const processImport = document.getElementById('processImport');
    const senderInput = document.getElementById('senderInput');
    const clipboardData = document.getElementById('clipboardData');

    if (!importFromClipboardBtn || !clipboardModal) return;

    // Show modal
    importFromClipboardBtn.addEventListener('click', function() {
        clipboardModal.classList.remove('hidden');
    });

    // Hide modal
    function hideModal() {
        clipboardModal.classList.add('hidden');
        if (senderInput) senderInput.value = '';
        if (clipboardData) clipboardData.value = '';
    }

    if (closeModal) closeModal.addEventListener('click', hideModal);
    if (cancelImport) cancelImport.addEventListener('click', hideModal);

    // Close modal when clicking outside
    clipboardModal.addEventListener('click', function(e) {
        if (e.target === clipboardModal) {
            hideModal();
        }
    });

    // Process import
    if (processImport) {
        processImport.addEventListener('click', async function() {
            const sender = senderInput ? senderInput.value.trim() : '';
            const data = clipboardData ? clipboardData.value.trim() : '';

            if (!sender) {
                alert('Please enter a sender name');
                return;
            }

            if (!data) {
                alert('Please paste the Excel data');
                return;
            }

            try {
                // Show loading state
                processImport.disabled = true;
                processImport.textContent = 'Processing...';

                // Split data into lines and clean up
                const items = data.split('\n')
                    .map(line => line.trim())
                    .filter(line => line.length > 0);

                if (items.length === 0) {
                    alert('No valid items found in the data');
                    return;
                }

                // Send to backend
                const response = await fetch('/api/import-clipboard-items', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sender: sender,
                        items: items
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    alert(`Successfully processed ${result.processed_count} items and saved to non_bought_items.json`);
                    hideModal();
                    // Optionally refresh the page to show new data
                    // window.location.reload();
                } else {
                    const error = await response.json();
                    alert(`Error processing items: ${error.detail}`);
                }
            } catch (error) {
                console.error('Error processing clipboard import:', error);
                alert('Error processing items. Please check the console for details.');
            } finally {
                // Reset button state
                processImport.disabled = false;
                processImport.textContent = 'Process Import';
            }
        });
    }
}

// Legacy functions for backward compatibility (from old chat-history.js)
function searchChatHistory(itemName, itemCode) {
    try {
        fetch(`/search-chat-history?item=${encodeURIComponent(itemCode)}`)
            .then((response) => {
                if (!response.ok) {
                    throw new Error("Failed to search chat history");
                }
                return response.json();
            })
            .then((data) => {
                // Get chat history from response
                let chatHistory = data.chat_history || [];

                // Sort by date (newest first)
                chatHistory.sort((a, b) => new Date(b.date) - new Date(a.date));

                // Create the popup with the chat history
                createChatHistoryPopup(itemName, chatHistory);
            })
            .catch((error) => {
                console.error("Error searching chat history:", error);
                if (typeof Toastify !== 'undefined') {
                    Toastify({
                        text: "Failed to search chat history: " + error.message,
                        duration: 3000,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#ef4444", // red-600
                        },
                    }).showToast();
                }
            });
    } catch (error) {
        console.error("Error searching chat history:", error);
        if (typeof Toastify !== 'undefined') {
            Toastify({
                text: "Failed to search chat history: " + error.message,
                duration: 3000,
                gravity: "top",
                position: "right",
                style: {
                    background: "#ef4444", // red-600
                },
            }).showToast();
        }
    }
}

function createChatHistoryPopup(itemName, chatHistory) {
    // Remove existing popup if any
    const existingPopup = document.getElementById("chatHistoryPopup");
    if (existingPopup) {
        existingPopup.remove();
    }

    // Create popup container
    const popup = document.createElement("div");
    popup.id = "chatHistoryPopup";
    popup.className = "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50";

    // Create popup content
    const popupContent = document.createElement("div");
    popupContent.className = "bg-gray-800 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-hidden flex flex-col";

    // Create header
    const header = document.createElement("div");
    header.className = "flex justify-between items-center mb-4";

    const title = document.createElement("h2");
    title.className = "text-xl font-bold text-white";
    title.textContent = `Chat History for: ${itemName}`;

    const closeButton = document.createElement("button");
    closeButton.className = "text-gray-400 hover:text-white text-2xl";
    closeButton.innerHTML = "&times;";
    closeButton.onclick = () => popup.remove();

    header.appendChild(title);
    header.appendChild(closeButton);

    // Create scrollable content area
    const scrollArea = document.createElement("div");
    scrollArea.className = "overflow-y-auto flex-1";

    if (chatHistory.length === 0) {
        const noData = document.createElement("p");
        noData.className = "text-gray-400 text-center py-8";
        noData.textContent = "No chat history found for this item.";
        scrollArea.appendChild(noData);
    } else {
        // Create table
        const table = document.createElement("table");
        table.className = "w-full text-sm text-left text-gray-300";

        // Create table header
        const thead = document.createElement("thead");
        thead.className = "text-xs text-gray-400 uppercase bg-gray-700";
        thead.innerHTML = `
            <tr>
                <th class="px-6 py-3">Date</th>
                <th class="px-6 py-3">User</th>
                <th class="px-6 py-3">Message</th>
                <th class="px-6 py-3">Action</th>
            </tr>
        `;

        // Create table body
        const tbody = document.createElement("tbody");
        chatHistory.forEach((entry, index) => {
            const row = document.createElement("tr");
            row.className = index % 2 === 0 ? "bg-gray-800" : "bg-gray-700";

            const dateCell = document.createElement("td");
            dateCell.className = "px-6 py-4";
            dateCell.textContent = entry.date || "N/A";

            const userCell = document.createElement("td");
            userCell.className = "px-6 py-4";
            userCell.textContent = entry.user || entry.sender || "Unknown";

            const messageCell = document.createElement("td");
            messageCell.className = "px-6 py-4 max-w-md";
            messageCell.textContent = entry.message || entry.text || "No message";

            const actionCell = document.createElement("td");
            actionCell.className = "px-6 py-4";
            const copyButton = document.createElement("button");
            copyButton.className = "bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs";
            copyButton.textContent = "Copy";
            copyButton.onclick = () => copyToClipboard(entry.message || entry.text || "");
            actionCell.appendChild(copyButton);

            row.appendChild(dateCell);
            row.appendChild(userCell);
            row.appendChild(messageCell);
            row.appendChild(actionCell);
            tbody.appendChild(row);
        });

        table.appendChild(thead);
        table.appendChild(tbody);
        scrollArea.appendChild(table);
    }

    popupContent.appendChild(header);
    popupContent.appendChild(scrollArea);
    popup.appendChild(popupContent);

    // Add to document
    document.body.appendChild(popup);

    // Close on outside click
    popup.addEventListener("click", (e) => {
        if (e.target === popup) {
            popup.remove();
        }
    });
}

function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        // Use the Clipboard API if available
        navigator.clipboard.writeText(text).then(() => {
            if (typeof Toastify !== 'undefined') {
                Toastify({
                    text: "Copied to clipboard!",
                    duration: 2000,
                    gravity: "top",
                    position: "right",
                    style: {
                        background: "#10b981", // green-500
                    },
                }).showToast();
            }
        }).catch(err => {
            console.error('Failed to copy: ', err);
            fallbackCopy(text);
        });
    } else {
        // Fallback for older browsers or non-secure contexts
        fallbackCopy(text);
    }
}

function fallbackCopy(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        if (typeof Toastify !== 'undefined') {
            Toastify({
                text: "Copied to clipboard!",
                duration: 2000,
                gravity: "top",
                position: "right",
                style: {
                    background: "#10b981", // green-500
                },
            }).showToast();
        }
    } catch (err) {
        console.error('Fallback copy failed: ', err);
        if (typeof Toastify !== 'undefined') {
            Toastify({
                text: "Failed to copy to clipboard",
                duration: 2000,
                gravity: "top",
                position: "right",
                style: {
                    background: "#ef4444", // red-500
                },
            }).showToast();
        }
    } finally {
        document.body.removeChild(textArea);
    }
}

// Export functions for global access
window.searchChatHistory = searchChatHistory;
window.copyToClipboard = copyToClipboard;
window.sortTable = window.sortTable; // Already defined above
