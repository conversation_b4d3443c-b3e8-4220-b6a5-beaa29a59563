<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stock Minimum Management - Toko LARIS LAX</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    backgroundColor: {
                        'gray-750': '#2d3748',
                        'gray-850': '#1a1a1a',
                        'gray-880': '#242424',
                        'gray-900': '#1e293b',
                    }
                }
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
</head>
<body class="bg-black text-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <div class="bg-gray-800 shadow-lg p-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-white">Stock Minimum Management</h1>
                    <p class="text-gray-400 mt-2">Set minimum stock levels for items with no minimum</p>
                </div>
                <button 
                    onclick="window.close()" 
                    class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg transition-colors duration-150"
                >
                    Close
                </button>
            </div>
        </div>

        <!-- Filters and Controls -->
        <div class="p-6 bg-gray-900">
            <div class="flex flex-wrap gap-4 items-center">
                <!-- Type Filter -->
                <div class="flex items-center gap-2">
                    <label class="text-gray-300">Filter by Type:</label>
                    <select 
                        id="jenisFilter" 
                        onchange="filterByJenis()"
                        class="bg-gray-700 text-white px-4 py-2 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
                    >
                        <option value="All" {% if selected_jenis == "All" %}selected{% endif %}>All</option>
                        {% for jenis in jenis_options %}
                        <option value="{{ jenis }}" {% if selected_jenis == jenis %}selected{% endif %}>{{ jenis }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Item Code Search -->
                <div class="flex items-center gap-2">
                    <label class="text-gray-300">Enter Kode Item:</label>
                    <input 
                        type="text" 
                        id="kodeItemInput"
                        placeholder="Enter item code"
                        class="bg-gray-700 text-white px-4 py-2 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
                    >
                    <button 
                        onclick="checkPrice()"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-150"
                    >
                        Check Price
                    </button>
                </div>

                <!-- Bulk Stock Minimum Setting -->
                <div class="flex items-center gap-2">
                    <label class="text-gray-300">Set all filtered stocks to:</label>
                    <input 
                        type="number" 
                        id="bulkStockMinInput"
                        placeholder="0.000"
                        step="1"
                        min="0"
                        onfocus="this.select()"
                        class="bg-gray-700 text-white px-4 py-2 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none w-32"
                    >
                    <button 
                        onclick="applyBulkStockMin()"
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-150"
                    >
                        Apply to All
                    </button>
                </div>
            </div>
        </div>

        <!-- Items Table -->
        <div class="p-6">
            <div class="bg-gray-800 rounded-lg overflow-hidden">
                <div class="overflow-x-auto max-h-[calc(100vh-300px)] overflow-y-auto">
                    <table class="w-full">
                        <thead class="bg-gray-700 sticky top-0">
                            <tr>
                                <th class="px-4 py-3 text-left text-gray-300 font-semibold">Kode Item</th>
                                <th class="px-4 py-3 text-left text-gray-300 font-semibold">Nama Item</th>
                                <th class="px-4 py-3 text-left text-gray-300 font-semibold">Merek</th>
                                <th class="px-4 py-3 text-left text-gray-300 font-semibold">Jenis</th>
                                <th class="px-4 py-3 text-left text-gray-300 font-semibold">Harga Jual</th>
                                <th class="px-4 py-3 text-left text-gray-300 font-semibold">Stock Min</th>
                                <th class="px-4 py-3 text-left text-gray-300 font-semibold">Current Stock</th>
                                <th class="px-4 py-3 text-left text-gray-300 font-semibold">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-700">
                            {% for item in items %}
                            <tr class="hover:bg-gray-750 transition-colors duration-150">
                                <td class="px-4 py-3 text-gray-100">
                                    <a 
                                        href="/item-history/{{ item.kodeitem }}" 
                                        target="_blank"
                                        class="text-blue-400 hover:text-blue-300 hover:underline cursor-pointer transition-colors duration-150"
                                    >
                                        {{ item.kodeitem }}
                                    </a>
                                </td>
                                <td class="px-4 py-3 text-gray-100">{{ item.namaitem }}</td>
                                <td class="px-4 py-3 text-gray-300">{{ item.merek }}</td>
                                <td class="px-4 py-3 text-gray-300">{{ item.jenis }}</td>
                                <td class="px-4 py-3 text-gray-100">{{ item.hargajual|currency }}</td>
                                <td class="px-4 py-3">
                                    <input 
                                        type="number" 
                                        id="stokmin_{{ item.kodeitem }}"
                                        value="{{ item.stokmin }}"
                                        step="1"
                                        min="0"
                                        onfocus="this.select()"
                                        class="bg-gray-700 text-white px-2 py-1 rounded border border-gray-600 focus:border-blue-500 focus:outline-none w-20"
                                    >
                                </td>
                                <td class="px-4 py-3 text-gray-100">{{ "%.3f"|format(item.stok) }}</td>
                                <td class="px-4 py-3">
                                    <button 
                                        onclick="updateStockMin('{{ item.kodeitem }}')"
                                        class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors duration-150"
                                    >
                                        Save
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Items Count -->
            <div class="mt-4 text-gray-400 text-sm">
                {{ items_count }} items found
            </div>
        </div>
    </div>

    <!-- Save Changes Button -->
    <div class="fixed bottom-6 right-6">
        <button 
            onclick="saveAllChanges()"
            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg shadow-lg transition-colors duration-150 flex items-center gap-2"
        >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Save Changes
        </button>
    </div>

    <script>
        function filterByJenis() {
            const jenis = document.getElementById('jenisFilter').value;
            const url = new URL(window.location);
            if (jenis === 'All') {
                url.searchParams.delete('jenis');
            } else {
                url.searchParams.set('jenis', jenis);
            }
            window.location.href = url.toString();
        }

        function checkPrice() {
            const kodeItem = document.getElementById('kodeItemInput').value.trim();
            if (!kodeItem) {
                showToast('Please enter an item code', 'error');
                return;
            }

            fetch(`/check-item-price?kodeitem=${encodeURIComponent(kodeItem)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(`${data.namaitem}: ${formatCurrency(data.hargajual)}`, 'success');
                    } else {
                        showToast(data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('Error checking item price', 'error');
                });
        }

        function updateStockMin(kodeitem) {
            const stokminInput = document.getElementById(`stokmin_${kodeitem}`);
            const stokmin = parseFloat(stokminInput.value);

            if (isNaN(stokmin) || stokmin < 0) {
                showToast('Please enter a valid stock minimum value', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('kodeitem', kodeitem);
            formData.append('stokmin', stokmin);

            fetch('/update-stock-minimum', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                } else {
                    showToast(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Error updating stock minimum', 'error');
            });
        }

        function saveAllChanges() {
            const inputs = document.querySelectorAll('input[id^="stokmin_"]');
            let updateCount = 0;
            let totalUpdates = inputs.length;

            if (totalUpdates === 0) {
                showToast('No items to update', 'info');
                return;
            }

            inputs.forEach(input => {
                const kodeitem = input.id.replace('stokmin_', '');
                const stokmin = parseFloat(input.value);

                if (!isNaN(stokmin) && stokmin >= 0) {
                    const formData = new FormData();
                    formData.append('kodeitem', kodeitem);
                    formData.append('stokmin', stokmin);

                    fetch('/update-stock-minimum', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        updateCount++;
                        if (updateCount === totalUpdates) {
                            showToast(`All ${totalUpdates} items updated successfully`, 'success');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        updateCount++;
                        if (updateCount === totalUpdates) {
                            showToast('Some updates may have failed', 'warning');
                        }
                    });
                }
            });
        }

        function applyBulkStockMin() {
            const bulkValue = document.getElementById('bulkStockMinInput').value;
            
            if (!bulkValue || bulkValue === '') {
                showToast('Please enter a stock minimum value', 'error');
                return;
            }

            const stokmin = parseFloat(bulkValue);
            if (isNaN(stokmin) || stokmin < 0) {
                showToast('Please enter a valid stock minimum value', 'error');
                return;
            }

            // Get all visible stock minimum inputs (filtered items)
            const visibleInputs = document.querySelectorAll('tbody tr:not([style*="display: none"]) input[id^="stokmin_"]');
            
            if (visibleInputs.length === 0) {
                showToast('No items to update', 'info');
                return;
            }

            // Set the value for all visible inputs
            visibleInputs.forEach(input => {
                input.value = stokmin;
            });

            showToast(`Set stock minimum to ${stokmin} for ${visibleInputs.length} filtered items`, 'success');
            
            // Clear the bulk input
            document.getElementById('bulkStockMinInput').value = '';
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        }

        function showToast(message, type = 'info') {
            const backgroundColor = {
                'success': '#10B981',
                'error': '#EF4444',
                'warning': '#F59E0B',
                'info': '#3B82F6'
            };

            Toastify({
                text: message,
                duration: 3000,
                gravity: "top",
                position: "right",
                style: {
                    background: backgroundColor[type] || backgroundColor['info']
                }
            }).showToast();
        }
    </script>
</body>
</html>