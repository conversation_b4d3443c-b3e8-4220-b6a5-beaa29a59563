from fastapi import APIRouter, Request, HTTPException, Depends
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from database.database_connection import get_db
from database.database_model import Item
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel
import uuid
import json
import os
from pathlib import Path

router = APIRouter()
templates = Jinja2Templates(directory="templates")

# Pydantic models for request/response
class MarketplacePurchaseItem(BaseModel):
    item_name: str
    quantity: float
    received: bool
    kodeitem: Optional[str] = None  # For existing items from DB

class MarketplacePurchaseRequest(BaseModel):
    purchase_date: str
    items: List[MarketplacePurchaseItem]

class MarketplacePurchaseResponse(BaseModel):
    id: str
    purchase_date: datetime
    item_name: str
    quantity: float
    received: bool
    kodeitem: Optional[str]
    created_at: datetime

# JSON file path for marketplace purchases
MARKETPLACE_PURCHASES_FILE = "marketplace_purchases.json"

def load_purchases_from_json():
    """Load purchases from JSON file"""
    if not os.path.exists(MARKETPLACE_PURCHASES_FILE):
        return []
    
    try:
        with open(MARKETPLACE_PURCHASES_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return []

def save_purchases_to_json(purchases):
    """Save purchases to JSON file"""
    with open(MARKETPLACE_PURCHASES_FILE, 'w', encoding='utf-8') as f:
        json.dump(purchases, f, indent=2, ensure_ascii=False, default=str)

@router.get("/marketplace-purchases", response_class=HTMLResponse)
async def get_marketplace_purchases_page(request: Request):
    """Render the marketplace purchases page"""
    return templates.TemplateResponse("marketplace_purchases.html", {"request": request})

@router.get("/api/search-items")
async def search_items(q: str, db: Session = Depends(get_db)):
    """Search for items in the database"""
    try:
        # Search items by name or code
        items = db.query(Item).filter(
            (Item.namaitem.ilike(f"%{q}%")) | 
            (Item.kodeitem.ilike(f"%{q}%"))
        ).limit(10).all()
        
        return [
            {
                "kodeitem": item.kodeitem,
                "namaitem": item.namaitem,
                "jenis": item.jenis,
                "satuan": item.satuan
            }
            for item in items
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/api/marketplace-purchases")
async def create_marketplace_purchase(purchase_data: MarketplacePurchaseRequest):
    """Create new marketplace purchase records"""
    try:
        purchase_date = datetime.strptime(purchase_data.purchase_date, "%Y-%m-%d")
        purchases = load_purchases_from_json()
        created_purchases = []
        
        for item in purchase_data.items:
            purchase_id = str(uuid.uuid4())
            
            new_purchase = {
                "id": purchase_id,
                "purchase_date": purchase_date.isoformat(),
                "item_name": item.item_name,
                "quantity": item.quantity,
                "received": item.received,
                "kodeitem": item.kodeitem,
                "created_at": datetime.utcnow().isoformat()
            }
            
            purchases.append(new_purchase)
            created_purchases.append({
                "id": purchase_id,
                "item_name": item.item_name,
                "quantity": item.quantity,
                "received": item.received
            })
        
        save_purchases_to_json(purchases)
        
        return {
            "success": True,
            "message": f"Successfully added {len(created_purchases)} items",
            "purchases": created_purchases
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/marketplace-purchases")
async def get_marketplace_purchases():
    """Get all marketplace purchases"""
    try:
        purchases = load_purchases_from_json()
        
        # Sort by purchase_date and created_at in descending order
        purchases.sort(key=lambda x: (x.get('purchase_date', ''), x.get('created_at', '')), reverse=True)
        
        return [
            {
                "id": purchase["id"],
                "purchase_date": datetime.fromisoformat(purchase["purchase_date"]).strftime("%d-%b-%Y").lower(),
                "item_name": purchase["item_name"],
                "quantity": purchase["quantity"],
                "received": purchase["received"],
                "kodeitem": purchase.get("kodeitem"),
                "created_at": datetime.fromisoformat(purchase["created_at"]).strftime("%d-%b-%Y %H:%M").lower()
            }
            for purchase in purchases
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/api/marketplace-purchases/{purchase_id}/received")
async def update_received_status(purchase_id: str, request: Request):
    """Update the received status of a marketplace purchase"""
    try:
        body = await request.json()
        received = body.get('received', False)
        
        purchases = load_purchases_from_json()
        
        purchase_found = False
        for purchase in purchases:
            if purchase["id"] == purchase_id:
                purchase["received"] = received
                purchase_found = True
                break
        
        if not purchase_found:
            raise HTTPException(status_code=404, detail="Purchase not found")
        
        save_purchases_to_json(purchases)
        
        return {
            "success": True,
            "message": "Received status updated successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/api/marketplace-purchases/{purchase_id}")
async def update_marketplace_purchase(purchase_id: str, request: Request):
    """Update marketplace purchase details (item name, quantity, date)"""
    try:
        body = await request.json()
        item_name = body.get('item_name')
        quantity = body.get('quantity')
        purchase_date = body.get('purchase_date')
        
        if not item_name or not quantity or not purchase_date:
            raise HTTPException(status_code=400, detail="Missing required fields")
        
        purchases = load_purchases_from_json()
        
        purchase_found = False
        for purchase in purchases:
            if purchase["id"] == purchase_id:
                purchase["item_name"] = item_name
                purchase["quantity"] = float(quantity)
                purchase["purchase_date"] = purchase_date
                purchase_found = True
                break
        
        if not purchase_found:
            raise HTTPException(status_code=404, detail="Purchase not found")
        
        save_purchases_to_json(purchases)
        
        return {
            "success": True,
            "message": "Purchase updated successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/api/marketplace-purchases/{purchase_id}")
async def delete_marketplace_purchase(purchase_id: str):
    """Delete a marketplace purchase"""
    try:
        purchases = load_purchases_from_json()
        
        original_length = len(purchases)
        purchases = [p for p in purchases if p["id"] != purchase_id]
        
        if len(purchases) == original_length:
            raise HTTPException(status_code=404, detail="Purchase not found")
        
        save_purchases_to_json(purchases)
        
        return {
            "success": True,
            "message": "Purchase deleted successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))