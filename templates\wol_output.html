<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WOL Toko Output</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: "class",
            theme: {
                extend: {
                    backgroundColor: {
                        "gray-750": "#2d3748",
                        "gray-850": "#1a1a1a",
                        "gray-880": "#242424",
                        "gray-900": "#1e293b",
                    },
                },
            },
        };
    </script>
</head>
<body class="bg-black text-gray-100">
    <div class="min-h-screen">
        <!-- Navbar -->
        <nav class="bg-gray-800 shadow-lg sticky top-0 z-50">
            <div class="max-w-[95%] mx-auto px-4">
                <div class="flex justify-between h-16">
                    <div class="flex items-center space-x-4">
                        <h1 class="text-3xl font-bold text-white">WOL Toko Output</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button
                            onclick="location.reload()"
                            class="px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-150"
                        >
                            Run Again
                        </button>
                        <button
                            onclick="window.close()"
                            class="px-6 py-2 text-lg bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-150"
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="max-w-[95%] mx-auto px-4 py-8">
            <!-- Status Card -->
            <div class="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
                <div class="flex items-center space-x-3 mb-4">
                    {% if success %}
                        <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                        <h2 class="text-xl font-semibold text-green-400">Success</h2>
                    {% else %}
                        <div class="w-4 h-4 bg-red-500 rounded-full"></div>
                        <h2 class="text-xl font-semibold text-red-400">Error</h2>
                    {% endif %}
                </div>
                <p class="text-gray-300">{{ message }}</p>
            </div>

            <!-- Console Output -->
            {% if output %}
            <div class="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
                <h3 class="text-lg font-semibold mb-4 text-green-400">Console Output</h3>
                <div class="bg-black rounded-lg p-4 font-mono text-sm overflow-x-auto">
                    <pre class="text-green-400 whitespace-pre-wrap">{{ output }}</pre>
                </div>
            </div>
            {% endif %}

            <!-- Error Output -->
            {% if error %}
            <div class="bg-gray-800 rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold mb-4 text-red-400">Error Details</h3>
                <div class="bg-black rounded-lg p-4 font-mono text-sm overflow-x-auto">
                    <pre class="text-red-400 whitespace-pre-wrap">{{ error }}</pre>
                </div>
            </div>
            {% endif %}

            <!-- Script Info -->
            <div class="bg-gray-800 rounded-lg shadow-lg p-6 mt-6">
                <h3 class="text-lg font-semibold mb-4 text-blue-400">Script Information</h3>
                <div class="space-y-2 text-sm">
                    <div class="flex">
                        <span class="text-gray-400 w-24">Script Path:</span>
                        <span class="text-gray-300 font-mono">H:\Python\FinalApp\WakeOnLanWOL_Toko.py</span>
                    </div>
                    <div class="flex">
                        <span class="text-gray-400 w-24">Timeout:</span>
                        <span class="text-gray-300">50 seconds</span>
                    </div>
                    <div class="flex">
                        <span class="text-gray-400 w-24">Executed:</span>
                        <span class="text-gray-300" id="timestamp"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Set current timestamp
        document.getElementById('timestamp').textContent = new Date().toLocaleString();
        
        // Auto-scroll to bottom if there's a lot of output
        window.addEventListener('load', function() {
            const outputElements = document.querySelectorAll('pre');
            outputElements.forEach(element => {
                if (element.scrollHeight > element.clientHeight) {
                    element.scrollTop = element.scrollHeight;
                }
            });
        });
    </script>
</body>
</html>