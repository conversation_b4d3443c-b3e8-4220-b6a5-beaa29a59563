from fastapi import APIRouter, Depends, Request
from sqlalchemy import func, text, and_
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import calendar
import pytz
from decimal import Decimal
from database.database_connection import get_db
from database.database_model import Item<PERSON><PERSON><PERSON>eader, ItemSalesDetail, ItemUnitConversion
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates

router = APIRouter()
templates = Jinja2Templates(directory="templates")

# Add currency filter
def currency_filter(value):
    try:
        return f"Rp {int(value):,}"
    except (ValueError, TypeError):
        return "Rp 0"

# Register the currency filter
templates.env.filters["currency"] = currency_filter

def get_current_db():
    return next(get_db("current"))

def get_old_db():
    return next(get_db("old"))

def determine_database_for_year(year: int, db_current: Session, db_old: Session):
    """
    Dynamically determine which database contains data for a specific year
    by checking both databases for transaction records in that year.
    """
    jakarta_tz = pytz.timezone('Asia/Jakarta')
    start_date = datetime(year, 1, 1, tzinfo=jakarta_tz)
    end_date = datetime(year + 1, 1, 1, tzinfo=jakarta_tz)
    
    # Check current database for data in the specified year
    current_db_count = db_current.query(ItemSalesHeader).filter(
        ItemSalesHeader.tanggal >= start_date,
        ItemSalesHeader.tanggal < end_date,
        ItemSalesHeader.tipe == 'KSR'
    ).count()
    
    # Check old database for data in the specified year
    old_db_count = db_old.query(ItemSalesHeader).filter(
        ItemSalesHeader.tanggal >= start_date,
        ItemSalesHeader.tanggal < end_date,
        ItemSalesHeader.tipe == 'KSR'
    ).count()
    
    # Return the database with more records, or current DB if both have data
    if old_db_count > current_db_count:
        return db_old
    else:
        return db_current

@router.get("/monthly-profit", response_class=HTMLResponse)
async def get_monthly_profit(request: Request, db: Session = Depends(get_current_db), db_old: Session = Depends(get_old_db)):
    jakarta_tz = pytz.timezone('Asia/Jakarta')
    current_date = datetime.now(jakarta_tz)
    
    # Get current month and year
    current_year = current_date.year
    last_year = current_year - 1

    # Determine which database to use for each year
    current_year_db = determine_database_for_year(current_year, db, db_old)
    last_year_db = determine_database_for_year(last_year, db, db_old)

    # Process current year data
    current_months = []
    last_year_months = []

    # Get all months for both years
    for month in range(1, 13):
        # Current year
        start_date = datetime(current_year, month, 1, tzinfo=jakarta_tz)
        if month < 12:
            end_date = datetime(current_year, month + 1, 1, tzinfo=jakarta_tz)
        else:
            end_date = datetime(current_year + 1, 1, 1, tzinfo=jakarta_tz)

        sales = current_year_db.query(
            func.sum(ItemSalesHeader.totalakhir).label('gross_sales')
        ).filter(
            ItemSalesHeader.tanggal >= start_date,
            ItemSalesHeader.tanggal < end_date,
            ItemSalesHeader.tipe == 'KSR'
        ).scalar() or 0

        # Calculate net profit using proper joins
        net_profit = current_year_db.query(
            func.sum(
                ItemSalesDetail.total - (ItemUnitConversion.hargapokok * ItemSalesDetail.jumlah)
            ).label('net_profit')
        ).join(
            ItemSalesHeader,
            ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
        ).outerjoin(
            ItemUnitConversion,
            and_(
                ItemSalesDetail.kodeitem == ItemUnitConversion.kodeitem,
                ItemSalesDetail.satuan == ItemUnitConversion.satuan
            )
        ).filter(
            ItemSalesHeader.tanggal >= start_date,
            ItemSalesHeader.tanggal < end_date,
            ItemSalesHeader.tipe == 'KSR'
        ).scalar() or 0

        current_months.append({
            'name': calendar.month_name[month],
            'gross_sales': float(sales),
            'net_profit': float(net_profit)
        })

        # Last year
        start_date = datetime(last_year, month, 1, tzinfo=jakarta_tz)
        if month < 12:
            end_date = datetime(last_year, month + 1, 1, tzinfo=jakarta_tz)
        else:
            end_date = datetime(last_year + 1, 1, 1, tzinfo=jakarta_tz)
        
        sales = last_year_db.query(
            func.sum(ItemSalesHeader.totalakhir).label('gross_sales')
        ).filter(
            ItemSalesHeader.tanggal >= start_date,
            ItemSalesHeader.tanggal < end_date,
            ItemSalesHeader.tipe == 'KSR'
        ).scalar() or 0

        net_profit = last_year_db.query(
            func.sum(
                ItemSalesDetail.total - (ItemUnitConversion.hargapokok * ItemSalesDetail.jumlah)
            ).label('net_profit')
        ).join(
            ItemSalesHeader,
            ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
        ).outerjoin(
            ItemUnitConversion,
            and_(
                ItemSalesDetail.kodeitem == ItemUnitConversion.kodeitem,
                ItemSalesDetail.satuan == ItemUnitConversion.satuan
            )
        ).filter(
            ItemSalesHeader.tanggal >= start_date,
            ItemSalesHeader.tanggal < end_date,
            ItemSalesHeader.tipe == 'KSR'
        ).scalar() or 0

        last_year_months.append({
            'name': calendar.month_name[month],
            'gross_sales': float(sales),
            'net_profit': float(net_profit)
        })

    return templates.TemplateResponse(
        "monthly_profit.html",
        {
            "request": request,
            "current_year": current_year,
            "last_year": last_year,
            "current_months": current_months,
            "last_year_months": last_year_months
        }
    )
