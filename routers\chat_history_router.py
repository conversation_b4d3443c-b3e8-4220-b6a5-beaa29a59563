from fastapi import APIRouter, Request, HTTPException, Depends
from fastapi.responses import HTMLResponse, JSONResponse, StreamingResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from typing import List
from datetime import datetime, timedelta
import json
import os
import pytz
import re
import pandas as pd
import asyncio
import shutil
from sqlalchemy import text
from sqlalchemy.orm import Session
from database.database_connection import get_db
from config import CHAT_HISTORY_PATH
from telethon import TelegramClient, events
import io

router = APIRouter()
templates = Jinja2Templates(directory="templates")

# Telegram API credentials
API_ID = '27446563'  # Get from `https://my.telegram.org`
API_HASH = '7eb34408a523b57d51569fef68421249'
BOT_TOKEN = '7288942301:AAEzgqvaMZVirnp2g4-W8GTFWEDkfpHvayM'
GROUP_ID = -541029745

# Initialize client
client = TelegramClient('session_name', API_ID, API_HASH)

def detect_barcode_in_text(text: str):
    """
    Detect barcode patterns in text (8, 10, 12, or 13 digits)
    """
    # Pattern to match 8, 10, 12, or 13 consecutive digits
    barcode_pattern = r'\b(?:\d{8}|\d{10}|\d{12}|\d{13})\b'
    matches = re.findall(barcode_pattern, text)
    return matches[0] if matches else None

def get_kodeitem_from_barcode(barcode: str, db):
    """
    Get kodeitem from barcode using tbl_itemsatuanjml
    """
    try:
        query = text("""
            SELECT kodeitem FROM tbl_itemsatuanjml 
            WHERE kodebarcode = :barcode
            LIMIT 1
        """)
        
        result = db.execute(query, {"barcode": barcode})
        row = result.fetchone()
        
        if row:
            return row[0]
        
        return None
    except Exception as e:
        print(f"Error getting kodeitem from barcode {barcode}: {e}")
        return None

def get_kodeitem_from_name(item_name: str, db):
    """
    Convert item name to kodeitem by checking for barcode first, then item name lookup
    """
    try:
        # First, check if the item name contains a barcode
        barcode = detect_barcode_in_text(item_name)
        if barcode:
            kodeitem = get_kodeitem_from_barcode(barcode, db)
            if kodeitem:
                return kodeitem
        
        # If no barcode found or barcode lookup failed, proceed with name lookup
        # Remove 'No BUY' from item name (case insensitive)
        clean_name = item_name.replace('No BUY', '').replace('No Buy', '').strip()

        
        # Query to find kodeitem - use ONLY exact match to prevent mix-ups between similar names
        query = text("""
            SELECT kodeitem, namaitem FROM tbl_item 
            WHERE LOWER(namaitem) = LOWER(:item_name)
            LIMIT 1
        """)
        
        result = db.execute(query, {
            "item_name": clean_name
        })
        row = result.fetchone()
        
        if row:
            return row[0]
        
        return None
    except Exception as e:
        print(f"Error getting kodeitem for {item_name}: {e}")
        return None

def get_item_jenis(kodeitem: str, db):
    """
    Get jenis (item type) from tbl_item using kodeitem
    """
    try:
        query = text("""
            SELECT jenis FROM tbl_item 
            WHERE kodeitem = :kodeitem
            LIMIT 1
        """)
        
        result = db.execute(query, {"kodeitem": kodeitem})
        row = result.fetchone()
        
        if row:
            return row[0]
        
        return None
    except Exception as e:
        print(f"Error getting jenis for kodeitem {kodeitem}: {e}")
        return None

def get_item_stock(kodeitem: str, db):
    """
    Get stock from tbl_itemstok using kodeitem (always in unit dasar D)
    """
    try:
        query = text("""
            SELECT stok FROM tbl_itemstok 
            WHERE kodeitem = :kodeitem
            LIMIT 1
        """)
        
        result = db.execute(query, {"kodeitem": kodeitem})
        row = result.fetchone()
        
        if row:
            return float(row[0]) if row[0] is not None else 0.0
        
        return 0.0
    except Exception as e:
        print(f"Error getting stock for kodeitem {kodeitem}: {e}")
        return 0.0

def get_item_buying_price(kodeitem: str, db):
    """
    Get buying price (hargapokok) from tbl_itemsatuanjml using kodeitem
    """
    try:
        query = text("""
            SELECT hargapokok FROM tbl_itemsatuanjml 
            WHERE kodeitem = :kodeitem AND tipe = 'D'
            LIMIT 1
        """)
        
        result = db.execute(query, {"kodeitem": kodeitem})
        row = result.fetchone()
        
        if row:
            return float(row[0]) if row[0] is not None else 0.0
        
        return 0.0
    except Exception as e:
        print(f"Error getting buying price for kodeitem {kodeitem}: {e}")
        return 0.0

def check_latest_transaction(kodeitem: str, message_date: datetime, db):
    """
    Check if the latest BL or PN transaction for an item is newer than the message date
    """
    try:

        # Query to find the latest BL, PN, SAH, or SA transaction
        query = text("""
            SELECT MAX(h.tanggal) as latest_date
            FROM tbl_imhd h
            JOIN tbl_imdt d ON h.notransaksi = d.notransaksi
            WHERE d.kodeitem = :kodeitem
            AND h.tipe IN ('BL', 'PN', 'SAH', 'SA')
        """)
        
        result = db.execute(query, {"kodeitem": kodeitem})
        row = result.fetchone()
        
        if row and row[0]:
            latest_transaction_date = row[0]

            
            # Convert to datetime if it's not already
            if isinstance(latest_transaction_date, str):
                latest_transaction_date = datetime.fromisoformat(latest_transaction_date.replace('Z', '+00:00'))
            
            # Ensure both dates are timezone-aware for proper comparison
            jakarta_tz = pytz.timezone('Asia/Jakarta')
            
            # Convert latest_transaction_date to Jakarta timezone if it has timezone info
            if latest_transaction_date.tzinfo is not None:
                latest_transaction_date = latest_transaction_date.astimezone(jakarta_tz)
            else:
                # If no timezone info, assume it's already in Jakarta timezone
                latest_transaction_date = jakarta_tz.localize(latest_transaction_date)
            
            # Ensure message_date is also in Jakarta timezone
            if message_date.tzinfo is None:
                message_date = jakarta_tz.localize(message_date)
            else:
                message_date = message_date.astimezone(jakarta_tz)
            
            # Compare dates - transaction should be AFTER message date to strikethrough
            is_newer = latest_transaction_date > message_date
            return is_newer
        

        return False
    except Exception as e:
        print(f"Error checking latest transaction for {kodeitem}: {e}")
        return False

@router.get("/chat-history", response_class=HTMLResponse)
async def chat_history_page(request: Request):
    chat_history_path = CHAT_HISTORY_PATH
    non_bought_items_path = "non_bought_items.json"
    
    # Get current date and 3 months ago
    jakarta_tz = pytz.timezone('Asia/Jakarta')
    current_date = datetime.now(jakarta_tz)
    three_months_ago = current_date - timedelta(days=90)
    
    chat_data = []
    
    # Load existing non-bought items
    existing_non_bought = []
    latest_saved_date = None
    
    if os.path.exists(non_bought_items_path):
        try:
            with open(non_bought_items_path, 'r', encoding='utf-8') as file:
                existing_non_bought = json.load(file)
                
                # Find the latest date in saved non-bought items
                for item in existing_non_bought:
                    if 'date' in item:
                        try:
                            item_date = datetime.fromisoformat(item['date'].replace('Z', '+00:00'))
                            if item_date.tzinfo is None:
                                item_date = jakarta_tz.localize(item_date)
                            else:
                                item_date = item_date.astimezone(jakarta_tz)
                            
                            # Truncate microseconds for comparison
                            item_date = item_date.replace(microsecond=0)
                            
                            if latest_saved_date is None or item_date > latest_saved_date:
                                latest_saved_date = item_date
                        except (ValueError, TypeError):
                            continue
        except Exception as e:
            print(f"Error loading non-bought items: {e}")
            existing_non_bought = []
    

    
    try:
        if os.path.exists(chat_history_path):
            with open(chat_history_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
                
                # Get database connection
                db_gen = get_db()
                db = next(db_gen)
                
                try:
                    # Filter data for new entries (newer than latest saved date) and last 3 months
                    new_entries = []
                    for entry in data:
                        entry_should_be_included = False
                        entry_date = None
                        
                        if 'date' in entry:
                            try:
                                # Parse the date from the entry
                                entry_date = datetime.fromisoformat(entry['date'].replace('Z', '+00:00'))
                                # Convert to Jakarta timezone if needed
                                if entry_date.tzinfo is None:
                                    entry_date = jakarta_tz.localize(entry_date)
                                else:
                                    entry_date = entry_date.astimezone(jakarta_tz)
                                
                                # Check if entry is within last 3 months AND newer than latest saved date
                                if entry_date >= three_months_ago:
                                    if latest_saved_date is None:
                                        entry_should_be_included = True
                                    else:
                                        # Truncate microseconds for comparison to avoid duplicates
                                        entry_date_truncated = entry_date.replace(microsecond=0)
                                        latest_saved_date_truncated = latest_saved_date.replace(microsecond=0)
                                        
                                        # Only include entries strictly newer than the latest saved date (ignoring microseconds)
                                        if entry_date_truncated > latest_saved_date_truncated:
                                            entry_should_be_included = True
                            except (ValueError, TypeError):
                                # If date parsing fails, include the entry if no saved date exists
                                if latest_saved_date is None:
                                    entry_should_be_included = True
                        else:
                            # If no date field, include the entry if no saved date exists
                            if latest_saved_date is None:
                                entry_should_be_included = True
                        
                        if entry_should_be_included:
                            # Check if message should be struck through
                            entry['strikethrough'] = False
                            
                            # Get the message content
                            message_content = entry.get('message') or entry.get('text') or entry.get('content')
                            
                            # Always try to get kodeitem if message content exists
                            if message_content:
                                # Get kodeitem from message (item name)
                                kodeitem = get_kodeitem_from_name(message_content, db)
                                
                                if kodeitem:
                                    # Store kodeitem in entry for linking to item history
                                    entry['kodeitem'] = kodeitem
                                    
                                    # Get and store jenis (item type)
                                    jenis = get_item_jenis(kodeitem, db)
                                    if jenis:
                                        entry['jenis'] = jenis
                                    
                                    # Get and store stock (always in unit dasar D)
                                    stock = get_item_stock(kodeitem, db)
                                    entry['stock'] = stock
                                    
                                    # Get and store buying price (hargapokok)
                                    buying_price = get_item_buying_price(kodeitem, db)
                                    entry['buying_price'] = buying_price
                                    
                                    # Only check for strikethrough if we have entry_date
                                    if entry_date:
                                        should_strikethrough = check_latest_transaction(kodeitem, entry_date, db)
                                        if should_strikethrough:
                                            entry['strikethrough'] = True
                            
                            new_entries.append(entry)
                            
                finally:
                    db.close()
                    
        # Process existing non-bought items for strikethrough
        if existing_non_bought:
            db = next(get_db())
            try:
                for item in existing_non_bought:
                    item['strikethrough'] = False
                    
                    # Get the message content
                    message_content = item.get('message') or item.get('text') or item.get('content')
                    
                    if message_content:
                        # Get kodeitem from message (item name)
                        kodeitem = get_kodeitem_from_name(message_content, db)
                        
                        if kodeitem:
                            # Store kodeitem in entry for linking to item history
                            item['kodeitem'] = kodeitem
                            
                            # Get and store jenis (item type)
                            jenis = get_item_jenis(kodeitem, db)
                            if jenis:
                                item['jenis'] = jenis
                            
                            # Get and store stock (always in unit dasar D)
                            stock = get_item_stock(kodeitem, db)
                            item['stock'] = stock
                            
                            # Get and store buying price (hargapokok)
                            buying_price = get_item_buying_price(kodeitem, db)
                            item['buying_price'] = buying_price
                            
                            # Check for strikethrough based on current date
                            if 'date' in item:
                                try:
                                    item_date = datetime.fromisoformat(item['date'].replace('Z', '+00:00'))
                                    if item_date.tzinfo is None:
                                        item_date = jakarta_tz.localize(item_date)
                                    else:
                                        item_date = item_date.astimezone(jakarta_tz)
                                    
                                    should_strikethrough = check_latest_transaction(kodeitem, item_date, db)
                                    if should_strikethrough:
                                        item['strikethrough'] = True
                                except (ValueError, TypeError):
                                    pass
            finally:
                db.close()
        
        # Combine existing non-bought items with new entries and remove duplicates
        all_entries = existing_non_bought + new_entries
        
        # Remove duplicates based on item name only, keeping the newest entry
        item_groups = {}
        
        for entry in all_entries:
            # Get the message content (item name)
            message_content = entry.get('message', '') or entry.get('text', '') or entry.get('content', '')
            
            if message_content:
                # Parse entry date for comparison
                entry_date = None
                if 'date' in entry:
                    try:
                        entry_date = datetime.fromisoformat(entry['date'].replace('Z', '+00:00'))
                        if entry_date.tzinfo is None:
                            jakarta_tz = pytz.timezone('Asia/Jakarta')
                            entry_date = jakarta_tz.localize(entry_date)
                        else:
                            entry_date = entry_date.astimezone(pytz.timezone('Asia/Jakarta'))
                    except (ValueError, TypeError):
                        entry_date = None
                
                # If this item name hasn't been seen or current entry is newer, keep it
                if message_content not in item_groups:
                    item_groups[message_content] = entry
                else:
                    # Compare dates and keep the newer entry
                    existing_entry = item_groups[message_content]
                    existing_date = None
                    
                    if 'date' in existing_entry:
                        try:
                            existing_date = datetime.fromisoformat(existing_entry['date'].replace('Z', '+00:00'))
                            if existing_date.tzinfo is None:
                                jakarta_tz = pytz.timezone('Asia/Jakarta')
                                existing_date = jakarta_tz.localize(existing_date)
                            else:
                                existing_date = existing_date.astimezone(pytz.timezone('Asia/Jakarta'))
                        except (ValueError, TypeError):
                            existing_date = None
                    
                    # Keep the entry with the newer date, or current if dates are invalid
                    if entry_date and existing_date:
                        if entry_date > existing_date:
                            item_groups[message_content] = entry
                    elif entry_date and not existing_date:
                        item_groups[message_content] = entry
            else:
                # If no message content, add to a special group to avoid losing entries
                empty_key = f"empty_{len(item_groups)}"
                item_groups[empty_key] = entry
        
        # Convert back to list
        chat_data = list(item_groups.values())
        
        # Sort by date (newest first)
        chat_data.sort(key=lambda x: x.get('date', ''), reverse=True)
        
    except Exception as e:
        print(f"Error reading chat history: {e}")
        # If there's an error, still show existing non-bought items if available
        if existing_non_bought:
            chat_data = existing_non_bought
            chat_data.append({"error": f"Failed to load new chat history: {str(e)}"})
        else:
            chat_data = [{"error": f"Failed to load chat history: {str(e)}"}]
    
    return templates.TemplateResponse(
        "chat_history.html", 
        {
            "request": request, 
            "chat_data": chat_data,
            "total_entries": len(chat_data)
        }
    )

# Pydantic model for non-bought items
class NonBoughtItem(BaseModel):
    date: str
    user: str
    message: str

@router.post("/save-non-bought-items")
async def save_non_bought_items(items: List[NonBoughtItem]):
    try:
            
        filename = f"non_bought_items.json"
        
        # Convert new items to dict format - we'll replace the entire file with current selection
        new_items_data = [item.dict() for item in items]
        
        # Use only the new items (don't merge with existing file)
        # This ensures ticked items are properly excluded
        all_items = new_items_data
        
        # Remove duplicates based on item name only, keeping the newest entry
        item_groups = {}
        
        for item in all_items:
            # Get the message content (item name)
            message_content = item.get('message', '')
            
            if message_content:
                # Parse item date for comparison
                item_date = None
                if 'date' in item:
                    try:
                        item_date = datetime.fromisoformat(item['date'].replace('Z', '+00:00'))
                        if item_date.tzinfo is None:
                            jakarta_tz = pytz.timezone('Asia/Jakarta')
                            item_date = jakarta_tz.localize(item_date)
                        else:
                            item_date = item_date.astimezone(pytz.timezone('Asia/Jakarta'))
                    except (ValueError, TypeError):
                        item_date = None
                
                # If this item name hasn't been seen or current item is newer, keep it
                if message_content not in item_groups:
                    item_groups[message_content] = item
                else:
                    # Compare dates and keep the newer item
                    existing_item = item_groups[message_content]
                    existing_date = None
                    
                    if 'date' in existing_item:
                        try:
                            existing_date = datetime.fromisoformat(existing_item['date'].replace('Z', '+00:00'))
                            if existing_date.tzinfo is None:
                                jakarta_tz = pytz.timezone('Asia/Jakarta')
                                existing_date = jakarta_tz.localize(existing_date)
                            else:
                                existing_date = existing_date.astimezone(pytz.timezone('Asia/Jakarta'))
                        except (ValueError, TypeError):
                            existing_date = None
                    
                    # Keep the item with the newer date, or current if dates are invalid
                    if item_date and existing_date:
                        if item_date > existing_date:
                            item_groups[message_content] = item
                    elif item_date and not existing_date:
                        item_groups[message_content] = item
            else:
                # If no message content, add to a special group to avoid losing items
                empty_key = f"empty_{len(item_groups)}"
                item_groups[empty_key] = item
        
        # Convert back to list
        unique_items = list(item_groups.values())
        
        # Save deduplicated items to JSON file
        with open(filename, 'w', encoding='utf-8') as file:
            json.dump(unique_items, file, ensure_ascii=False, indent=2)
        
        return JSONResponse(
            content={
                "message": f"Successfully saved {len(unique_items)} non-bought items (deduplicated from {len(all_items)} total)",
                "filename": filename,
                "count": len(unique_items),
                "original_count": len(items),
                "duplicates_removed": len(all_items) - len(unique_items)
            },
            status_code=200
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error saving non-bought items: {str(e)}"
        )

async def get_messages():
    """Get messages from Telegram group from the last 4 months"""
    # Calculate date 4 months ago
    four_months_ago = datetime.now() - timedelta(days=120)
     
    messages = []
    async with client:
        async for message in client.iter_messages(GROUP_ID):
            if message.text and message.date.replace(tzinfo=None) >= four_months_ago:  # Only process text messages within 4 months
                messages.append({
                    'date': message.date.strftime('%d-%m-%Y %H:%M'),
                    'user': message.sender.username if message.sender else 'Unknown',
                    'message': message.text
                })
     
    return pd.DataFrame(messages)

def save_messages_to_file(df, filename='chat_history.json'):
    """Save messages to a JSON file"""
    # Convert date column to ISO format for consistent storage
    df_to_save = df.copy()
    df_to_save['date'] = pd.to_datetime(df_to_save['date'], format='%d-%m-%Y %H:%M')
    df_to_save.to_json(filename, orient='records', date_format='iso')
    print(f"Chat history saved to {filename}")

@router.post("/update-telegram-messages")
async def update_telegram_messages():
    """Update chat history with latest messages from Telegram"""
    try:
        # Get messages from Telegram
        df = await get_messages()
        
        if df.empty:
            return JSONResponse(
                content={
                    "message": "No new messages found",
                    "message_count": 0
                },
                status_code=200
            )
        
        # Save messages to file
        save_messages_to_file(df, 'chat_history.json')
        
        return JSONResponse(
            content={
                "message": "Successfully updated Telegram messages",
                "message_count": len(df)
            },
            status_code=200
        )
        
    except Exception as e:
        print(f"Error updating Telegram messages: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error updating Telegram messages: {str(e)}"
        )

class ExportRequest(BaseModel):
    items: List[dict]
    search_text: str = ""

@router.post("/export-chat-history-excel")
async def export_chat_history_to_excel(request: ExportRequest, db: Session = Depends(get_db)):
    """Export the filtered/visible chat history items to Excel"""
    try:
        jakarta_tz = pytz.timezone('Asia/Jakarta')
        
        # Prepare data for Excel export from the provided filtered items
        export_data = []
        for entry in request.items:
            # Format date for display
            formatted_date = ""
            if 'date' in entry:
                try:
                    entry_date = datetime.fromisoformat(entry['date'].replace('Z', '+00:00'))
                    if entry_date.tzinfo is None:
                        entry_date = jakarta_tz.localize(entry_date)
                    else:
                        entry_date = entry_date.astimezone(jakarta_tz)
                    formatted_date = entry_date.strftime('%d-%m-%Y %H:%M')
                except (ValueError, TypeError):
                    formatted_date = entry.get('date', '')
            
            export_data.append({
                'Date': formatted_date,
                'User': entry.get('user', ''),
                'Message': entry.get('message', '') or entry.get('text', '') or entry.get('content', ''),
                'Kode Item': entry.get('kodeitem', ''),
                'Jenis': entry.get('jenis', ''),
                'Stock': entry.get('stock', ''),
                'Buying Price': entry.get('buying_price', ''),
                'Strikethrough': 'Yes' if entry.get('strikethrough', False) else 'No'
            })
        
        # Create DataFrame
        df = pd.DataFrame(export_data)
        
        # Generate filename with search text and current timestamp
        current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Clean search text for filename (remove invalid characters)
        clean_search_text = ""
        if request.search_text:
            # Replace invalid filename characters with underscores
            clean_search_text = re.sub(r'[<>:"/\\|?*]', '_', request.search_text.strip())
            # Limit length to prevent overly long filenames
            clean_search_text = clean_search_text[:50]
            filename = f"{clean_search_text}_chat_history_export_{current_time}.xlsx"
        else:
            filename = f"chat_history_export_{current_time}.xlsx"
        
        # Save directly to E:\Download folder
        download_path = f"E:\\Download\\{filename}"
        
        # Create the directory if it doesn't exist
        os.makedirs("E:\\Download", exist_ok=True)
        
        # Create Excel file and save to the specified path
        with pd.ExcelWriter(download_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Chat History', index=False)
            
            # Get the workbook and worksheet
            workbook = writer.book
            worksheet = writer.sheets['Chat History']
            
            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        return JSONResponse(
            content={
                "message": f"Successfully exported {len(export_data)} items to {download_path}",
                "filename": filename,
                "path": download_path,
                "count": len(export_data)
            },
            status_code=200
        )
        
    except Exception as e:
        print(f"Error exporting chat history to Excel: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error exporting chat history to Excel: {str(e)}"
        )

# Pydantic model for clipboard import
class ClipboardImportRequest(BaseModel):
    sender: str
    items: List[str]

@router.post("/api/import-clipboard-items")
async def import_clipboard_items(request: ClipboardImportRequest, db: Session = Depends(get_db)):
    """
    Import items from clipboard data, fetch DB info (jenis, stock, buying price) and save to non_bought_items.json
    """
    try:
        jakarta_tz = pytz.timezone('Asia/Jakarta')
        current_date = datetime.now(jakarta_tz)
        
        processed_items = []
        
        for item_name in request.items:
            item_name = item_name.strip()
            if not item_name:
                continue
                
            # Try to find item in database by name (case-insensitive partial match)
            query = text("""
                SELECT i.kodeitem, i.namaitem, i.jenis, 
                       COALESCE(s.stok, 0) as stock,
                       COALESCE(
                           (SELECT harga FROM tbl_imdt 
                            WHERE kodeitem = i.kodeitem 
                            ORDER BY dateupd DESC LIMIT 1), 0
                       ) as buying_price
                FROM tbl_item i
                LEFT JOIN tbl_itemstok s ON i.kodeitem = s.kodeitem
                WHERE LOWER(i.namaitem) LIKE LOWER(:item_name_pattern)
                ORDER BY 
                    CASE 
                        WHEN LOWER(i.namaitem) = LOWER(:item_name_exact) THEN 1
                        WHEN LOWER(i.namaitem) LIKE LOWER(:exact_start) THEN 2
                        ELSE 3
                    END,
                    LENGTH(i.namaitem)
                LIMIT 1
            """)
            
            result = db.execute(query, {
                'item_name_pattern': f'%{item_name}%',
                'item_name_exact': item_name,
                'exact_start': f'{item_name}%'
            }).fetchone()
            
            # Create item entry for non_bought_items.json
            item_entry = {
                "date": current_date.isoformat(),
                "user": request.sender,
                "message": item_name
            }
            
            # If found in DB, we could add additional info as comments (optional)
            if result:
                # Add DB info as a comment in the message (optional)
                db_info = f" (Jenis: {result.jenis or 'N/A'}, Stock: {result.stock}, Buying Price: {result.buying_price or 0})"
                # Uncomment the line below if you want to include DB info in the message
                # item_entry["message"] += db_info
            
            processed_items.append(item_entry)
        
        # Load existing non_bought_items.json
        non_bought_items_path = "non_bought_items.json"
        existing_items = []
        
        if os.path.exists(non_bought_items_path):
            try:
                with open(non_bought_items_path, 'r', encoding='utf-8') as file:
                    existing_items = json.load(file)
            except (json.JSONDecodeError, FileNotFoundError):
                existing_items = []
        
        # Combine new items with existing items (new items first to maintain chronological order)
        all_items = processed_items + existing_items
        
        # Remove duplicates based on message content, keeping the newest entry
        item_groups = {}
        
        for item in all_items:
            message_content = item.get('message', '')
            
            if message_content:
                # Parse item date for comparison
                item_date = None
                if 'date' in item:
                    try:
                        item_date = datetime.fromisoformat(item['date'].replace('Z', '+00:00'))
                        if item_date.tzinfo is None:
                            item_date = jakarta_tz.localize(item_date)
                        else:
                            item_date = item_date.astimezone(jakarta_tz)
                    except (ValueError, TypeError):
                        item_date = None
                
                # If this item name hasn't been seen or current item is newer, keep it
                if message_content not in item_groups:
                    item_groups[message_content] = item
                else:
                    # Compare dates and keep the newer item
                    existing_date = None
                    if 'date' in item_groups[message_content]:
                        try:
                            existing_date = datetime.fromisoformat(item_groups[message_content]['date'].replace('Z', '+00:00'))
                            if existing_date.tzinfo is None:
                                existing_date = jakarta_tz.localize(existing_date)
                            else:
                                existing_date = existing_date.astimezone(jakarta_tz)
                        except (ValueError, TypeError):
                            existing_date = None
                    
                    # Keep the newer item
                    if item_date and existing_date:
                        if item_date > existing_date:
                            item_groups[message_content] = item
                    elif item_date and not existing_date:
                        item_groups[message_content] = item
            else:
                # If no message content, add to a special group to avoid losing items
                empty_key = f"empty_{len(item_groups)}"
                item_groups[empty_key] = item
        
        # Convert back to list
        unique_items = list(item_groups.values())
        
        # Save to non_bought_items.json
        with open(non_bought_items_path, 'w', encoding='utf-8') as file:
            json.dump(unique_items, file, ensure_ascii=False, indent=2)
        
        return JSONResponse(
            content={
                "message": f"Successfully processed {len(processed_items)} items and saved to non_bought_items.json",
                "processed_count": len(processed_items),
                "total_items_in_file": len(unique_items),
                "duplicates_removed": len(all_items) - len(unique_items)
            },
            status_code=200
        )
        
    except Exception as e:
        print(f"Error importing clipboard items: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error importing clipboard items: {str(e)}"
        )