<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Toko LARIS LAX</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            backgroundColor: {
              "gray-750": "#2d3748",
              "gray-850": "#1a1a1a",
              "gray-880": "#242424",
              "gray-900": "#1e293b", // Adding another bg color that is more visible
            },
          },
        },
      };
    </script>
    <script src="/static/js/inactivity-timer.js"></script>
    <script src="/static/js/today-sales-details.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <link
      rel="stylesheet"
      type="text/css"
      href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css"
    />
    <script>
      function formatCurrency(amount) {
        return new Intl.NumberFormat("id-ID", {
          style: "currency",
          currency: "IDR",
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(amount);
      }

      function updateTodaySales() {
        // Set a 5-second timeout for the fetch request
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        fetch("/today-sales", { signal: controller.signal })
          .then((response) => {
            clearTimeout(timeoutId);
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
          })
          .then((data) => {
            document.getElementById("todaySales").textContent = formatCurrency(
              data.total_sales
            );
          })
          .catch((error) => {
            console.error("Error fetching today's sales:", error);
            // Show fallback value when connection fails
            document.getElementById("todaySales").textContent =
              formatCurrency(0);

            // Optional: Show a visual indicator that data is unavailable
            const salesElement = document.getElementById("todaySales");
            salesElement.style.opacity = "0.6";
            salesElement.title =
              "Data unavailable - database connection failed";
          });
      }

      // Update sales every 30 seconds
      setInterval(updateTodaySales, 30000);
    </script>
  </head>
  <body class="bg-black text-gray-100">
    <div class="min-h-screen" id="mainContent">
      <!-- Navbar -->
      <nav class="bg-gray-800 shadow-lg">
        <div class="max-w-[95%] mx-auto px-4">
          <!-- Changed from max-w-7xl -->
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <a
                href="/"
                class="text-3xl font-bold text-white hover:text-gray-300 transition-colors duration-150"
              >
                Toko LARIS LAX
              </a>
            </div>
            <div class="flex items-center space-x-4">
              <a
                href="/chat-history"
                target="_blank"
                class="bg-purple-600 rounded-lg px-4 py-2 hover:bg-purple-700 transition-colors duration-150 cursor-pointer"
              >
                <span class="text-white">Chat History</span>
              </a>
              <a
                href="/low-stock-items"
                target="_blank"
                class="bg-yellow-600 rounded-lg px-4 py-2 hover:bg-yellow-700 transition-colors duration-150 cursor-pointer"
              >
                <span class="text-white">Daily Low Stock</span>
              </a>
              <a
                href="/today-sales-details?date={{ datetime.now(pytz.timezone('Asia/Jakarta')).strftime('%Y-%m-%d') }}"
                target="_blank"
                rel="noopener noreferrer"
                class="bg-green-700 rounded-lg px-4 py-2 hover:bg-green-600 transition-colors duration-150 cursor-pointer group"
              >
                <span class="text-xl text-white group-hover:text-white"
                  >Today's Sales:</span
                >
                <span
                  id="todaySales"
                  class="text-2xl font-bold text-white ml-2 group-hover:text-white"
                >
                  <span class="text-white-400"
                    >{{ "Rp {:,.0f}".format(total_sales if total_sales is not
                    none else 0) }}</span
                  >
                </span>
              </a>
              <a
                href="/logout"
                class="bg-red-600 rounded-lg px-4 py-2 hover:bg-red-700 transition-colors duration-150 cursor-pointer"
              >
                <span class="text-white">Logout</span>
              </a>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-[95%] mx-auto px-4 py-4">
        <!-- Changed from max-w-7xl -->
        <!-- Search and Actions Bar -->
        <div
          class="mb-4 space-y-2 sm:flex sm:items-center sm:justify-between sm:space-y-0"
        >
          <!-- Search Bar -->
          <div class="flex-1 max-w-xl">
            <form method="get" action="/" class="flex gap-2">
              <div class="relative flex-1">
                <input
                  type="text"
                  id="searchBox"
                  name="search"
                  value="{{ search or '' }}"
                  placeholder="Search items..."
                  class="w-full px-4 py-2 text-lg rounded-lg border border-gray-600 bg-black text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150"
                />
              </div>
              <button
                type="submit"
                class="px-6 py-2 text-lg bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-150 flex items-center gap-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                    clip-rule="evenodd"
                  />
                </svg>
                Search
              </button>
            </form>
            {% if search or negative %}
            <div class="mt-2 text-gray-400">
              Row = {{ total_rows or 0 }}, Item = {{ total_items or 0 }}
            </div>
            {% endif %}
          </div>

          <!-- Action Buttons -->
          <div class="flex space-x-3">
            <!-- Increased space between buttons -->
            <button
              onclick="window.open('/three-month-sales', '_blank')"
              class="px-6 py-2 text-lg bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-150"
            >
              3-Month Sales
            </button>
            <button
              onclick="window.open('/twelve-month-sales', '_blank')"
              class="px-6 py-2 text-lg bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition duration-150"
            >
              12-Month Sales
            </button>
            <button
              onclick="searchNegative()"
              class="px-6 py-2 text-lg bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-150"
            >
              Negative
            </button>
            <button
              onclick="exportToExcel('{{ last_search_key }}')"
              class="px-6 py-2 text-lg bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-150"
              {%
              if
              not
              last_search_key
              %}disabled{%
              endif
              %}
            >
              Excel
            </button>
            <div class="relative inline-block text-left">
              <button
                onclick="toggleDropdown()"
                class="px-6 py-2 text-lg bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition duration-150"
              >
                More
                <svg
                  class="w-4 h-4 ml-2 inline-block"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
              </button>
              <div
                id="dropdownMenu"
                class="hidden absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-gray-700 ring-1 ring-black ring-opacity-5 z-50"
              >
                <div class="py-1">
                  <a
                    href="/daily-profit"
                    target="_blank"
                    class="block px-4 py-2 text-sm text-white hover:bg-gray-600 cursor-pointer"
                  >
                    Daily Profit
                  </a>
                  <a
                    href="/monthly-profit"
                    target="_blank"
                    class="block px-4 py-2 text-sm text-white hover:bg-gray-600 cursor-pointer"
                  >
                    Monthly Profit
                  </a>
                  <a
                    href="/monthly-profit-by-category"
                    target="_blank"
                    class="block px-4 py-2 text-sm text-white hover:bg-gray-600 cursor-pointer"
                  >
                    Monthly Profit by Category
                  </a>
                  <a
                    href="/monthly-low-stock-items"
                    target="_blank"
                    class="block px-4 py-2 text-sm text-white hover:bg-gray-600 cursor-pointer"
                  >
                    Monthly Low Stock
                  </a>
                  <a
                    href="/discounted-items"
                    target="_blank"
                    class="block px-4 py-2 text-sm text-white hover:bg-gray-600 cursor-pointer"
                  >
                    Discounted Items
                  </a>
                  <a
                    href="/order-items/{{ last_search_key }}"
                    target="_blank"
                    class="block px-4 py-2 text-sm text-white hover:bg-gray-600 cursor-pointer"
                    {%
                    if
                    not
                    last_search_key
                    %}style="pointer-events: none; opacity: 0.5;"
                    {%
                    endif
                    %}
                  >
                    Order Items
                  </a>
                  <a
                    href="/items-not-sold-by-time"
                    target="_blank"
                    class="block px-4 py-2 text-sm text-white hover:bg-gray-600 cursor-pointer"
                  >
                    Item Not Sold by Time
                  </a>
                  <a
                    href="/utilities"
                    target="_blank"
                    class="block px-4 py-2 text-sm text-white hover:bg-gray-600 cursor-pointer"
                  >
                    Utilities
                  </a>
                  <a
                    href="/stock-minimum-management"
                    target="_blank"
                    class="block px-4 py-2 text-sm text-white hover:bg-gray-600 cursor-pointer"
                  >
                    Stock Minimum Management
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Table -->
        <div class="overflow-x-auto h-[calc(100vh-220px)] overflow-y-auto">
          <!-- Debug info -->
          <div class="text-xs text-gray-500 mb-2">
            Search: {{ search|default('None') }}, Negative: {{
            negative|default('False') }}, Items count: {{ items|length }}, Last
            search key: {{ last_search_key|default('None') }}
          </div>

          {% if not search and negative == False %}
          <div class="text-center py-8 text-gray-400">
            <p class="text-xl">Enter a search term to find items</p>
            <p class="text-sm mt-2">
              Use the search box above or press '/' to start searching
            </p>
          </div>
          {% else %}
          <table class="min-w-full divide-y divide-gray-700">
            <!-- Changed from min-w-full -->
            <thead class="bg-gray-700">
              <tr>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600"
                  data-sort="kodeitem"
                  onclick="sortTable('kodeitem')"
                >
                  Kode Item
                  <span class="sort-indicator opacity-0 ml-1">↑</span>
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600"
                  data-sort="namaitem"
                  onclick="sortTable('namaitem')"
                >
                  Nama Item
                  <span class="sort-indicator opacity-0 ml-1">↑</span>
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600"
                  data-sort="stok"
                  onclick="sortTable('stok')"
                >
                  Stok
                  <span class="sort-indicator opacity-0 ml-1">↑</span>
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600"
                  data-sort="merek"
                  onclick="sortTable('merek')"
                >
                  Merek
                  <span class="sort-indicator opacity-0 ml-1">↑</span>
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600"
                  data-sort="jenis"
                  onclick="sortTable('jenis')"
                >
                  Jenis
                  <span class="sort-indicator opacity-0 ml-1">↑</span>
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600"
                  data-sort="harga"
                  onclick="sortTable('harga')"
                >
                  Harga
                  <span class="sort-indicator opacity-0 ml-1">↑</span>
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600"
                  data-sort="rak"
                  onclick="sortTable('rak')"
                >
                  Rak
                  <span class="sort-indicator opacity-0 ml-1">↑</span>
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600"
                  data-sort="keterangan"
                  onclick="sortTable('keterangan')"
                >
                  Keterangan
                  <span class="sort-indicator opacity-0 ml-1">↑</span>
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600"
                  data-sort="supplier"
                  onclick="sortTable('supplier')"
                >
                  Supplier
                  <span class="sort-indicator opacity-0 ml-1">↑</span>
                </th>
              </tr>
            </thead>
            <tbody class="bg-gray-800 divide-y divide-gray-700">
              {% set ns = namespace(previous_kodeitem=None, row_color=True) %}
              {% for item_data in items %} {% if item_data.item.kodeitem !=
              ns.previous_kodeitem %} {% set ns.row_color = not ns.row_color %}
              {% set ns.previous_kodeitem = item_data.item.kodeitem %} {% endif
              %} {% for unit in item_data.units %}
              <tr
                class="hover:bg-gray-700 transition duration-150 {{ 'bg-black' if ns.row_color else 'bg-gray-900' }}"
              >
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                  <a
                    href="/item-history/{{ item_data.item.kodeitem }}"
                    target="_blank"
                    class="hover:text-blue-400 transition-colors duration-150"
                  >
                    {{ item_data.item.kodeitem|limit(15) }}
                  </a>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                  <a
                    href="/edit-item/{{ item_data.item.kodeitem }}"
                    target="_blank"
                    class="hover:text-blue-400 transition-colors duration-150"
                  >
                    {{ item_data.item.namaitem }}
                  </a>
                </td>
                <td
                  class="px-4 py-3 whitespace-nowrap text-sm {% if item_data.stok <= item_data.item.stokmin %} text-red-400 {% elif item_data.item.stokmin == 0 %} bg-yellow-950 {% else %} text-gray-300 {% endif %}"
                >
                  {% if unit.tipe == 'D' %} {{ "%.2f"|format(item_data.stok) }}
                  {{ unit.satuan }} {% else %} {{
                  "%.2f"|format(unit.stok_in_unit) }} {{ unit.satuan }} {% endif
                  %}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                  {{ item_data.item.merek or '' }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                  <span
                    class="cursor-pointer hover:text-blue-400 transition-colors duration-150"
                    onclick="searchChatHistory('{{ item_data.item.namaitem|replace('\'', '\\\'') }}', '{{ item_data.item.kodeitem }}')"
                    title="Click to view chat history for this item"
                  >
                    {{ item_data.item.jenis or '' }}
                  </span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                  {{ unit.satuan }} ({{ unit.hargapokok|thousands }} / {{
                  unit.hargajual|thousands }})
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                  {{ item_data.item.rak or '' }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                  {{ item_data.item.keterangan or '' }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                  {{ item_data.item.supplier1 or '' }}
                </td>
              </tr>
              {% endfor %} {% endfor %}
            </tbody>
          </table>
          {% if items|length == 0 %}
          <div class="text-center py-8 text-gray-400">
            <p class="text-xl">No items found</p>
            <p class="text-sm mt-2">Try different search terms</p>
          </div>
          {% endif %} {% endif %}
        </div>
      </div>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const searchBox = document.getElementById("searchBox");
        const mainContent = document.getElementById("mainContent");

        // Function to focus and select search box text
        function focusAndSelectSearch() {
          searchBox.focus();
          searchBox.select();
        }

        // Focus and select search box text on page load
        focusAndSelectSearch();

        // Add click event listener to the main content
        mainContent.addEventListener("click", function (event) {
          // Check if clicked element is not an interactive element
          if (
            !event.target.matches(
              'button, input, a, select, textarea, [role="button"]'
            )
          ) {
            focusAndSelectSearch();
          }
        });

        // Add keyboard shortcut (pressing '/' key)
        document.addEventListener("keydown", function (event) {
          if (event.key === "/" && !event.target.matches("input, textarea")) {
            event.preventDefault();
            focusAndSelectSearch();
          }
        });

        // Focus and select after form submission and page load
        if (
          window.performance &&
          window.performance.navigation.type ===
            window.performance.navigation.TYPE_NAVIGATE
        ) {
          focusAndSelectSearch();
        }

        // Add copy on select functionality
        document.addEventListener("mouseup", function () {
          const selectedText = window.getSelection().toString().trim();
          if (selectedText) {
            // Try modern Clipboard API first
            if (navigator.clipboard && navigator.clipboard.writeText) {
              navigator.clipboard.writeText(selectedText).catch((err) => {
                console.error("Clipboard API failed: ", err);
                fallbackCopy(selectedText);
              });
            } else {
              // Fall back to older method
              fallbackCopy(selectedText);
            }
          }
        });

        // Fallback copy method using execCommand
        function fallbackCopy(text) {
          try {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed"; // Avoid scrolling to bottom
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.width = "2em";
            textArea.style.height = "2em";
            textArea.style.padding = "0";
            textArea.style.border = "none";
            textArea.style.outline = "none";
            textArea.style.boxShadow = "none";
            textArea.style.background = "transparent";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            const successful = document.execCommand("copy");
            document.body.removeChild(textArea);
            if (!successful) console.error("Fallback copy failed");
          } catch (err) {
            console.error("Fallback copy error: ", err);
          }
        }
      });
    </script>
    <script>
      function searchNegative() {
        // Create a new form to avoid any issues with existing form
        const form = document.createElement("form");
        form.method = "get";
        form.action = "/";

        // Add an empty search parameter
        const searchInput = document.createElement("input");
        searchInput.type = "hidden";
        searchInput.name = "search";
        searchInput.value = "";
        form.appendChild(searchInput);

        // Add the negative parameter
        const negativeInput = document.createElement("input");
        negativeInput.type = "hidden";
        negativeInput.name = "negative";
        negativeInput.value = "true";
        form.appendChild(negativeInput);

        // Add the form to the document and submit it
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
      }
    </script>
    <script>
      async function exportToExcel(searchKey) {
        if (!searchKey) {
          alert("Please perform a search first");
          return;
        }
        try {
          const response = await fetch(`/export-excel/${searchKey}`);
          if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to export to Excel");
          }
        } catch (error) {
          console.error("Failed to export to Excel:", error);
          alert(error.message || "Failed to export to Excel");
        }
      }
    </script>
    <script>
      function toggleDropdown() {
        const dropdown = document.getElementById("dropdownMenu");
        dropdown.classList.toggle("hidden");

        // Close dropdown when clicking outside
        document.addEventListener("click", function (event) {
          const isClickInside = event.target.closest(".relative");
          if (!isClickInside) {
            dropdown.classList.add("hidden");
          }
        });
      }
    </script>
    <script>
      let sortDirection = {
        kodeitem: "asc",
        namaitem: "asc",
        stok: "asc",
        merek: "asc",
        jenis: "asc",
        harga: "asc",
        rak: "asc",
        keterangan: "asc",
        supplier: "asc",
      };

      function sortTable(column) {
        const table = document.querySelector("table");
        const tbody = table.querySelector("tbody");
        const rows = Array.from(tbody.querySelectorAll("tr"));

        // Toggle sort direction
        sortDirection[column] =
          sortDirection[column] === "asc" ? "desc" : "asc";

        // Define column indices mapping
        const columnMap = {
          kodeitem: 0,
          namaitem: 1,
          stok: 2,
          merek: 3,
          jenis: 4,
          harga: 5,
          rak: 6,
          keterangan: 7,
          supplier: 8,
        };

        rows.sort((a, b) => {
          let aValue = a
            .querySelector(`td:nth-child(${columnMap[column] + 1})`)
            .textContent.trim();
          let bValue = b
            .querySelector(`td:nth-child(${columnMap[column] + 1})`)
            .textContent.trim();

          // Convert to numbers for numerical columns
          if (column === "stok" || column === "harga") {
            // Remove currency formatting and convert to number
            aValue = parseFloat(aValue.replace(/[^0-9.-]+/g, "")) || 0;
            bValue = parseFloat(bValue.replace(/[^0-9.-]+/g, "")) || 0;
            return sortDirection[column] === "asc"
              ? aValue - bValue
              : bValue - aValue;
          }

          // String comparison for text columns
          return sortDirection[column] === "asc"
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        });

        // Clear and repopulate tbody
        rows.forEach((row) => tbody.appendChild(row));

        // Update sort indicators
        updateSortIndicators(column);
      }

      function updateSortIndicators(activeColumn) {
        const headers = document.querySelectorAll("th[data-sort]");
        headers.forEach((header) => {
          const column = header.getAttribute("data-sort");
          const indicator = header.querySelector(".sort-indicator");

          if (column === activeColumn) {
            indicator.textContent = sortDirection[column] === "asc" ? "↑" : "↓";
            indicator.classList.remove("opacity-0");
          } else {
            indicator.classList.add("opacity-0");
          }
        });
      }
    </script>
  </body>
</html>
