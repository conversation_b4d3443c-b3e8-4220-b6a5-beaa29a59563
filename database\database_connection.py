from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base, scoped_session
from sqlalchemy.pool import QueuePool

# SQLALCHEMY_DATABASE_URL = "postgresql+psycopg2://admin:2356988@192.168.2.222:5444/i4_<PERSON><PERSON><PERSON>"
SQLALCHEMY_DATABASE_URL = "postgresql+psycopg2://admin:2356988@192.168.2.10:5444/i4_TokoLarisLAX"          # New and Current DB
SQLALCHEMY_DATABASE_URL_OLD = "postgresql+psycopg2://admin:2356988@192.168.2.222:5444/i4_TokoLarisLAXOld"   # Old DB until end of 2024 (Read-Only)


# Configure connection pool with custom settings
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    pool_size=20,  # Increased pool size
    max_overflow=10,  # Allow more connections beyond pool_size
    pool_timeout=3,  # Reduced timeout for acquiring a connection (was 10)
    pool_recycle=1800,  # Recycle connections after 30 minutes (1800 seconds)
    pool_pre_ping=True,  # Check connection before use
    poolclass=QueuePool, # Explicitly set poolclass to QueuePool
    echo=False, #set to True if you want to see sqlalchemy query logging in console.
    connect_args={
        "connect_timeout": 3,  # Add connection timeout at database level
        "application_name": "FastAPI_IPOS"
    }
)
SessionLocal = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=engine))

# Configure connection pool for the old database (read-only)
engine_old = create_engine(
    SQLALCHEMY_DATABASE_URL_OLD,
    pool_size=10,  # Smaller pool size for read-only
    max_overflow=5,
    pool_timeout=3,
    pool_recycle=3600,
    pool_pre_ping=True,
    poolclass=QueuePool,
    echo=False
)
SessionLocalOld = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=engine_old))

Base = declarative_base()

def get_db(db_type: str = "current"):
    if db_type == "current":
        db = SessionLocal() # Use the original SessionLocal
    elif db_type == "old":
        db = SessionLocalOld()
    else:
        raise ValueError("Invalid database type specified. Use 'current' or 'old'.")
    try:
        yield db
    finally:
        db.close()
