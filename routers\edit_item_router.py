from fastapi import APIRouter, Depends, Request, HTTPException, Query
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from sqlalchemy import func
from database.database_connection import get_db
from database.database_model import (
    Item, 
    ItemStock,
    ItemUnitConversion,
    ItemSellingPrice
)
from pydantic import BaseModel
from decimal import Decimal
import urllib.parse
import html

router = APIRouter(
    prefix="",
    tags=["edit_item"]
)

templates = Jinja2Templates(directory="templates")

# Add this new model for the update request
class ItemUpdate(BaseModel):
    namaitem: str
    merek: str
    jenis: str
    rak: str
    stokmin: float
    satuan: str
    harga_pokok: float
    harga_jual: float
    keterangan: str | None = None
    supplier1: str | None = None

@router.get("/edit-item/{kodeitem}", response_class=HTMLResponse)
async def edit_item(
    request: Request,
    kodeitem: str,
    db: Session = Depends(get_db)
):
    # Convert kodeitem to lowercase for comparison
    kodeitem = kodeitem.lower()
    
    # Get item details using case-insensitive comparison
    item = db.query(Item).filter(func.lower(Item.kodeitem) == kodeitem).first()
    
    if not item:
        raise HTTPException(status_code=404, detail="Item not found")
    
    # Get stock information
    stock = db.query(ItemStock.stok)\
        .filter(func.lower(ItemStock.kodeitem) == kodeitem)\
        .scalar() or 0
    
    # Get all unit conversions with their selling prices for this item
    results = db.query(
        ItemUnitConversion,
        ItemSellingPrice
    ).outerjoin(
        ItemSellingPrice,
        (ItemUnitConversion.kodeitem == ItemSellingPrice.kodeitem) &
        (ItemUnitConversion.satuan == ItemSellingPrice.satuan)
    ).filter(
        func.lower(ItemUnitConversion.kodeitem) == kodeitem
    ).all()
    
    # Get current unit
    current_unit = db.query(ItemUnitConversion)\
        .filter(
            func.lower(ItemUnitConversion.kodeitem) == kodeitem,
            ItemUnitConversion.tipe == 'D'
        ).first()

    # Process units and their converted stocks
    units_with_stock = []
    for unit_conv, selling_price in results:
        converted_stock = stock
        if unit_conv.tipe == 'K' and unit_conv.jumlahkonv:
            converted_stock = stock / unit_conv.jumlahkonv
        units_with_stock.append({
            'satuan': unit_conv.satuan,
            'converted_stock': converted_stock,
            'jumlahkonv': unit_conv.jumlahkonv,
            'tipe': unit_conv.tipe,
            'hargapokok': unit_conv.hargapokok,
            'hargajual': selling_price.hargajual if selling_price else 0
        })

    # Get all suppliers for dropdown - modified to handle empty values
    suppliers = db.query(Item.supplier1).distinct().order_by(Item.supplier1).all()
    suppliers = [s[0] for s in suppliers if s[0]]  # Only include non-empty suppliers
    
    # Get all brands for dropdown
    brands = db.query(Item.merek).distinct().order_by(Item.merek).all()
    brands = [b[0] for b in brands]  # Remove the if b[0] filter to allow empty values
    
    # Get all types for dropdown
    types = db.query(Item.jenis).distinct().order_by(Item.jenis).all()
    types = [t[0] for t in types if t[0]]

    # Get current unit's selling price
    current_selling_price = db.query(ItemSellingPrice.hargajual)\
        .filter(
            func.lower(ItemSellingPrice.kodeitem) == kodeitem,
            ItemSellingPrice.satuan == current_unit.satuan if current_unit else None
        ).scalar() or 0

    return templates.TemplateResponse(
        "edit_item.html",
        {
            "request": request,
            "item": item,
            "stock": stock,
            "unit": current_unit,
            "units_with_stock": units_with_stock,
            "suppliers": suppliers,
            "brands": brands,
            "types": types,
            "current_selling_price": current_selling_price
        }
    )

# Add a new endpoint to handle unit conversion
@router.get("/api/convert-stock")
async def convert_stock(
    kodeitem: str = Query(...),
    satuan: str = Query(...),
    db: Session = Depends(get_db)
):
    try:
        # Decode URL encoded characters and HTML entities
        decoded_kodeitem = urllib.parse.unquote(kodeitem)
        decoded_kodeitem = html.unescape(decoded_kodeitem)
        
        # Convert kodeitem to lowercase for comparison
        kodeitem_lower = decoded_kodeitem.lower()
        
        # Get base stock using case-insensitive comparison
        stock = db.query(ItemStock.stok)\
            .filter(func.lower(ItemStock.kodeitem) == kodeitem_lower)\
            .scalar() or 0
        
        # Get unit conversion and selling price for the selected unit
        unit_data = db.query(
            ItemUnitConversion,
            ItemSellingPrice.hargajual
        ).outerjoin(
            ItemSellingPrice,
            (func.lower(ItemUnitConversion.kodeitem) == func.lower(ItemSellingPrice.kodeitem)) &
            (ItemUnitConversion.satuan == ItemSellingPrice.satuan)
        ).filter(
            func.lower(ItemUnitConversion.kodeitem) == kodeitem_lower,
            ItemUnitConversion.satuan == satuan
        ).first()
        
        if not unit_data:
            # Debug: Let's check what units are available for this item
            available_units = db.query(ItemUnitConversion.satuan)\
                .filter(func.lower(ItemUnitConversion.kodeitem) == kodeitem_lower)\
                .all()
            available_units_list = [unit[0] for unit in available_units]
            raise HTTPException(
                status_code=404, 
                detail=f"Unit conversion not found for item '{decoded_kodeitem}' with unit '{satuan}'. Available units: {available_units_list}"
            )
        
        unit_conv, harga_jual = unit_data
        
        converted_stock = stock
        if unit_conv.tipe == 'K' and unit_conv.jumlahkonv:
            converted_stock = stock / unit_conv.jumlahkonv
        
        return {
            "converted_stock": converted_stock,
            "harga_pokok": float(unit_conv.hargapokok) if unit_conv.hargapokok else 0,
            "harga_jual": float(harga_jual) if harga_jual else 0
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update unit conversion. Please try again. Error: {str(e)}")

@router.post("/api/update-item/{kodeitem}")
async def update_item(
    kodeitem: str,
    item_update: ItemUpdate,
    db: Session = Depends(get_db)
):
    try:
        # Convert empty string to None (which becomes NULL in database)
        supplier = None if not item_update.supplier1 or item_update.supplier1.strip() == '' else item_update.supplier1

        # Update basic item information
        db.query(Item).filter(Item.kodeitem == kodeitem).update({
            'namaitem': item_update.namaitem,
            'merek': item_update.merek,
            'jenis': item_update.jenis,
            'rak': item_update.rak,
            'stokmin': item_update.stokmin,
            'keterangan': item_update.keterangan,
            'supplier1': supplier  # This will save as NULL in database when empty
        })

        # Update unit conversion (harga pokok)
        db.query(ItemUnitConversion).filter(
            ItemUnitConversion.kodeitem == kodeitem,
            ItemUnitConversion.satuan == item_update.satuan
        ).update({
            'hargapokok': item_update.harga_pokok
        })

        # Update or insert selling price
        existing_price = db.query(ItemSellingPrice).filter(
            ItemSellingPrice.kodeitem == kodeitem,
            ItemSellingPrice.satuan == item_update.satuan
        ).first()

        if existing_price:
            db.query(ItemSellingPrice).filter(
                ItemSellingPrice.kodeitem == kodeitem,
                ItemSellingPrice.satuan == item_update.satuan
            ).update({
                'hargajual': item_update.harga_jual
            })
        else:
            new_price = ItemSellingPrice(
                kodeitem=kodeitem,
                satuan=item_update.satuan,
                hargajual=item_update.harga_jual
            )
            db.add(new_price)

        db.commit()
        return {"success": True, "message": "Item updated successfully"}

    except Exception as e:
        db.rollback()
        print(f"Error updating database for item {kodeitem}: {str(e)}")  # Added console logging
        raise HTTPException(status_code=500, detail=str(e))
