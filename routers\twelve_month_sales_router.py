from fastapi import APIRouter, Depends, Request, HTTPException
from fastapi.responses import StreamingResponse, Response
from sqlalchemy import func, and_, or_
from sqlalchemy.orm import Session
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Optional
import pandas as pd
import io
import re
from pathlib import Path

from database.database_connection import get_db
from database.database_model import (
    Item, 
    ItemStock, 
    ItemUnitConversion, 
    ItemSellingPrice, 
    ItemSalesHeader, 
    ItemSalesDetail
)
from fastapi.templating import Jinja2Templates

#dont delete this note: needed is calculated: last year next 3 months sales - stock (dynamic based on current month)

router = APIRouter()
templates = Jinja2Templates(directory="templates")

search_history: Dict[str, tuple[pd.DataFrame, str]] = {}

def determine_database_for_month(year: int, month: int, db_current: Session, db_old: Session):
    """
    Determine which database contains data for a specific month/year
    by checking both databases for transaction records in that period.
    """
    # Create start and end dates for the month
    start_date = datetime(year, month, 1)
    if month == 12:
        end_date = datetime(year + 1, 1, 1)
    else:
        end_date = datetime(year, month + 1, 1)
    
    # Check current database for data in the specified month
    current_db_count = db_current.query(ItemSalesHeader).filter(
        ItemSalesHeader.tanggal >= start_date,
        ItemSalesHeader.tanggal < end_date,
        ItemSalesHeader.tipe == 'KSR'
    ).count()
    
    # Check old database for data in the specified month
    old_db_count = db_old.query(ItemSalesHeader).filter(
        ItemSalesHeader.tanggal >= start_date,
        ItemSalesHeader.tanggal < end_date,
        ItemSalesHeader.tipe == 'KSR'
    ).count()
    
    # Return the database with more records, or current DB if both have data
    if old_db_count > current_db_count:
        return db_old
    else:
        return db_current

def get_monthly_sales_for_item(kodeitem: str, year: int, month: int, db_current: Session, db_old: Session):
    """
    Get sales data for a specific item in a specific month, checking both databases.
    Priority: Use current DB if it has data, otherwise use old DB to avoid double counting.
    """
    # Create start and end dates for the month
    start_date = datetime(year, month, 1)
    if month == 12:
        end_date = datetime(year + 1, 1, 1)
    else:
        end_date = datetime(year, month + 1, 1)
    
    # Try current database first - with unit conversion
    current_sales_with_units = db_current.query(
        ItemSalesDetail.jumlah,
        ItemSalesDetail.satuan
    ).join(
        ItemSalesHeader,
        (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
        (ItemSalesHeader.tipe == 'KSR')
    ).filter(
        ItemSalesDetail.kodeitem == kodeitem,
        ItemSalesHeader.tanggal >= start_date,
        ItemSalesHeader.tanggal < end_date
    ).all()
    
    # Calculate total sales with unit conversion for current DB
    current_sales = 0
    for sale in current_sales_with_units:
        # Get conversion factor for this unit
        conversion = db_current.query(ItemUnitConversion.jumlahkonv, ItemUnitConversion.tipe)\
            .filter(
                ItemUnitConversion.kodeitem == kodeitem,
                ItemUnitConversion.satuan == sale.satuan
            ).first()
        
        if conversion:
            if conversion.tipe == 'K':  # Conversion unit
                current_sales += sale.jumlah * conversion.jumlahkonv
            else:  # Base unit
                current_sales += sale.jumlah
        else:
            # If no conversion found, use as is
            current_sales += sale.jumlah
    
    # If current database has data for this month, use it exclusively
    if current_sales > 0:
        return current_sales
    
    # Only check old database if current database has no data for this month - with unit conversion
    old_sales_with_units = db_old.query(
        ItemSalesDetail.jumlah,
        ItemSalesDetail.satuan
    ).join(
        ItemSalesHeader,
        (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
        (ItemSalesHeader.tipe == 'KSR')
    ).filter(
        ItemSalesDetail.kodeitem == kodeitem,
        ItemSalesHeader.tanggal >= start_date,
        ItemSalesHeader.tanggal < end_date
    ).all()
    
    # Calculate total sales with unit conversion for old DB
    old_sales = 0
    for sale in old_sales_with_units:
        # Get conversion factor for this unit
        conversion = db_old.query(ItemUnitConversion.jumlahkonv, ItemUnitConversion.tipe)\
            .filter(
                ItemUnitConversion.kodeitem == kodeitem,
                ItemUnitConversion.satuan == sale.satuan
            ).first()
        
        if conversion:
            if conversion.tipe == 'K':  # Conversion unit
                old_sales += sale.jumlah * conversion.jumlahkonv
            else:  # Base unit
                old_sales += sale.jumlah
        else:
            # If no conversion found, use as is
            old_sales += sale.jumlah
    
    return old_sales
    
    return old_sales

@router.get("/twelve-month-sales")
async def twelve_month_sales(
    request: Request,
    search: Optional[str] = None,
    distinct: Optional[str] = None,
    db: Session = Depends(get_db)
):
    items_data = []
    total_rows = 0
    total_items = 0
    
    if search:
        # Get old database connection
        old_db_gen = get_db("old")
        old_db = next(old_db_gen)
        
        try:
            # Split search terms and remove empty strings
            search_terms = [term.strip() for term in search.split() if term.strip()]
            
            # Base query
            query = db.query(
                Item,
                ItemStock.stok,
                ItemUnitConversion.satuan,
                ItemUnitConversion.tipe,
                ItemUnitConversion.jumlahkonv,
                ItemUnitConversion.hargapokok,
                ItemSellingPrice.hargajual
            ).join(
                ItemStock, 
                (Item.kodeitem == ItemStock.kodeitem)
            ).join(
                ItemUnitConversion,
                (Item.kodeitem == ItemUnitConversion.kodeitem)
            ).outerjoin(
                ItemSellingPrice,
                (Item.kodeitem == ItemSellingPrice.kodeitem) &
                (ItemUnitConversion.satuan == ItemSellingPrice.satuan)
            )

            # Apply search filters
            for term in search_terms:
                query = query.filter(
                    func.lower(
                        Item.kodeitem + ' ' +
                        Item.namaitem + ' ' + 
                        func.coalesce(Item.merek, '') + ' ' +
                        func.coalesce(Item.jenis, '') + ' ' + 
                        func.coalesce(Item.keterangan, '') + ' ' + 
                        func.coalesce(Item.supplier1, '')
                    ).contains(term.lower())
                )

            results = query.all()

            # Get current date for calculations
            current_date = datetime.now()
            one_year_ago = current_date - timedelta(days=365)

            # Calculate monthly sales for each item (last 12 months)
            monthly_sales = {}
            for result in results:
                kodeitem = result.Item.kodeitem
                monthly_data = []
                has_sales = False
                
                # Get current month and year
                current_month = current_date.month
                current_year = current_date.year
                
                # Calculate sales for last 12 months (starting from current month going backwards)
                for i in range(12):
                    # Calculate month and year going backwards
                    month = current_month - i
                    year = current_year
                    
                    # Handle year rollover
                    if month <= 0:
                        month += 12
                        year -= 1
                    
                    # Get sales for this month from both databases
                    month_sales = get_monthly_sales_for_item(kodeitem, year, month, db, old_db)
                    
                    if month_sales > 0:
                        has_sales = True
                    monthly_data.append(str(int(month_sales)))
                
                if has_sales:
                    # Reverse the list so it shows oldest to newest (12 months ago to current)
                    monthly_data.reverse()
                    monthly_sales[kodeitem] = '-'.join(monthly_data)
                else:
                    monthly_sales[kodeitem] = ''

            # Calculate yearly sales for stock needed calculation
            # Use last year's same 3-month period for prediction
            current_date = datetime.now()
            current_month = current_date.month
            current_year = current_date.year
            
            # Calculate the next 3-month period from last year
            # For example: if current is Sep 2025, use Oct 2024, Nov 2024, Dec 2024
            # If current is May 2025, use Jun 2024, Jul 2024, Aug 2024
            last_year = current_year - 1

            # Start from next month of current month
            start_month = current_month + 1
            start_year = last_year

            # Handle year wrapping for start month
            if start_month > 12:
                start_month -= 12
                start_year += 1

            # Start date: beginning of the calculated start month last year
            last_year_start = datetime(start_year, start_month, 1)

            # Calculate end month (start_month + 3)
            end_month = start_month + 3
            end_year = start_year
            if end_month > 12:
                end_month -= 12
                end_year += 1

            # End date: beginning of the month after the 3-month period
            last_year_end = datetime(end_year, end_month, 1)
            
            one_year_ago = current_date - timedelta(days=365)
            
            # Get all item codes from results
            item_codes = [r.Item.kodeitem for r in results]
            
            # Check if current DB has data for all 12 months for any item
            current_db_has_complete_data = {}
            for kodeitem in item_codes:
                months_with_data = 0
                current_month = current_date.month
                current_year = current_date.year
                
                for i in range(12):
                    month = current_month - i
                    year = current_year
                    
                    if month <= 0:
                        month += 12
                        year -= 1
                    
                    start_date = datetime(year, month, 1)
                    if month == 12:
                        end_date = datetime(year + 1, 1, 1)
                    else:
                        end_date = datetime(year, month + 1, 1)
                    
                    # Check if current DB has data for this month
                    month_data = db.query(ItemSalesHeader).filter(
                        ItemSalesHeader.tanggal >= start_date,
                        ItemSalesHeader.tanggal < end_date,
                        ItemSalesHeader.tipe == 'KSR'
                    ).join(
                        ItemSalesDetail,
                        ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
                    ).filter(
                        ItemSalesDetail.kodeitem == kodeitem
                    ).count()
                    
                    if month_data > 0:
                        months_with_data += 1
                
                # If current DB has data for at least 6 months, consider it has sufficient data
                current_db_has_complete_data[kodeitem] = months_with_data >= 6
            
            # Calculate last year's 3-month sales for stock prediction
            # Query current database for last year's same 3-month period
            last_year_sales_current = db.query(
                ItemSalesDetail.kodeitem,
                func.sum(ItemSalesDetail.jumlah).label('total_sales')
            ).join(
                ItemSalesHeader,
                (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
                (ItemSalesHeader.tipe == 'KSR')
            ).filter(
                ItemSalesHeader.tanggal >= last_year_start,
                ItemSalesHeader.tanggal < last_year_end,
                ItemSalesDetail.kodeitem.in_(item_codes)
            ).group_by(
                ItemSalesDetail.kodeitem
            ).all()
            
            # Only query old DB for items that don't have data in current DB for last year period
            items_needing_old_data = []
            current_db_last_year_items = {item.kodeitem for item in last_year_sales_current}
            items_needing_old_data = [kodeitem for kodeitem in item_codes 
                                    if kodeitem not in current_db_last_year_items]
            
            last_year_sales_old = []
            if items_needing_old_data:
                last_year_sales_old = old_db.query(
                    ItemSalesDetail.kodeitem,
                    func.sum(ItemSalesDetail.jumlah).label('total_sales')
                ).join(
                    ItemSalesHeader,
                    (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
                    (ItemSalesHeader.tipe == 'KSR')
                ).filter(
                    ItemSalesHeader.tanggal >= last_year_start,
                    ItemSalesHeader.tanggal < last_year_end,
                    ItemSalesDetail.kodeitem.in_(items_needing_old_data)
                ).group_by(
                    ItemSalesDetail.kodeitem
                ).all()
            
            # Combine last year's 3-month sales data intelligently to avoid double counting
            last_year_sales_dict = {}
            for item in last_year_sales_current:
                last_year_sales_dict[item.kodeitem] = item.total_sales or 0
            
            # Only add from old DB if item doesn't exist in current DB results
            for item in last_year_sales_old:
                if item.kodeitem not in last_year_sales_dict:
                    last_year_sales_dict[item.kodeitem] = item.total_sales or 0
            
            # Calculate yearly sales based on database availability
            yearly_sales_current = db.query(
                ItemSalesDetail.kodeitem,
                func.sum(ItemSalesDetail.jumlah).label('total_sales')
            ).join(
                ItemSalesHeader,
                (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
                (ItemSalesHeader.tipe == 'KSR')
            ).filter(
                ItemSalesHeader.tanggal >= one_year_ago,
                ItemSalesDetail.kodeitem.in_(item_codes)
            ).group_by(
                ItemSalesDetail.kodeitem
            ).all()
            
            # Only query old DB for items that don't have complete data in current DB
            items_needing_old_data = [kodeitem for kodeitem in item_codes 
                                    if not current_db_has_complete_data.get(kodeitem, False)]
            
            yearly_sales_old = []
            if items_needing_old_data:
                yearly_sales_old = old_db.query(
                    ItemSalesDetail.kodeitem,
                    func.sum(ItemSalesDetail.jumlah).label('total_sales')
                ).join(
                    ItemSalesHeader,
                    (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
                    (ItemSalesHeader.tipe == 'KSR')
                ).filter(
                    ItemSalesHeader.tanggal >= one_year_ago,
                    ItemSalesDetail.kodeitem.in_(items_needing_old_data)
                ).group_by(
                    ItemSalesDetail.kodeitem
                ).all()
            
            # Combine sales data intelligently to avoid double counting
            sales_dict = {}
            for item in yearly_sales_current:
                sales_dict[item.kodeitem] = item.total_sales or 0
            
            # Only add old DB data for items that don't have complete current DB data
            # AND only if current DB doesn't already have data for that item
            for item in yearly_sales_old:
                if not current_db_has_complete_data.get(item.kodeitem, False):
                    # Only add old DB data if current DB has no data at all for this item
                    if item.kodeitem not in sales_dict or sales_dict[item.kodeitem] == 0:
                        sales_dict[item.kodeitem] = item.total_sales or 0
                    else:
                        sales_dict[item.kodeitem] = item.total_sales or 0

            # Process results with proper unit conversion
            for result in results:
                kodeitem = result.Item.kodeitem
                
                # Calculate stock needed based on last year's same 3-month period
                last_year_3month_sale = last_year_sales_dict.get(kodeitem, 0)
                predicted_need = last_year_3month_sale  # Use last year's same period to predict this year's need
                
                # Get base stock (always in base unit)
                current_stock = result.stok if result.stok is not None else 0
                
                # Calculate if stock is low (always compare in base units)
                is_low_stock = current_stock <= result.Item.stokmin
                
                # Convert stock for display if needed
                converted_stock = current_stock
                if hasattr(result, 'tipe') and result.tipe == 'K' and hasattr(result, 'jumlahkonv') and result.jumlahkonv:
                    converted_stock = current_stock / result.jumlahkonv
                
                needed = predicted_need - converted_stock  # ❌ WRONG: Using converted_stock instead of current_stock
                stock_needed = round(needed, 2) if needed > 0 else None

                items_data.append({
                    'item': result.Item,
                    'stok': converted_stock,
                    'base_stock': current_stock,
                    'is_low_stock': is_low_stock,
                    'last_12months_sales': monthly_sales.get(kodeitem, ''),
                    'stock_needed_3month': stock_needed,
                    'hargapokok': result.hargapokok,
                    'hargajual': result.hargajual,
                    'satuan': result.satuan,
                    'tipe': result.tipe
                })

            # Apply distinct filter if enabled
            is_distinct = distinct == 'true'
            if is_distinct:
                # Create a dictionary to store unique items by kodeitem
                unique_items = {}
                for item in items_data:
                    kodeitem = item['item'].kodeitem
                    if kodeitem not in unique_items:
                        unique_items[kodeitem] = item
                items_data = list(unique_items.values())

            # Calculate counts after filtering
            total_rows = len(items_data)
            total_items = len(set(item['item'].kodeitem for item in items_data))
            
        finally:
            old_db.close()

    return templates.TemplateResponse(
        "twelve_month_sales.html",
        {
            "request": request,
            "items": items_data,
            "search": search,
            "total_rows": total_rows,
            "total_items": total_items,
            "distinct": is_distinct if search else False
        }
    )


@router.get("/twelve-month-sales/export")
async def export_twelve_month_sales(
    request: Request,
    search: Optional[str] = None,
    distinct: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Export twelve month sales data to Excel file
    """
    items_data = []

    if search:
        # Get old database connection
        old_db_gen = get_db("old")
        old_db = next(old_db_gen)

        try:
            # Split search terms and remove empty strings
            search_terms = [term.strip() for term in search.split() if term.strip()]

            # Base query (same as main route)
            query = db.query(
                Item,
                ItemStock.stok,
                ItemUnitConversion.satuan,
                ItemUnitConversion.tipe,
                ItemUnitConversion.jumlahkonv,
                ItemUnitConversion.hargapokok,
                ItemSellingPrice.hargajual
            ).join(
                ItemStock,
                (Item.kodeitem == ItemStock.kodeitem)
            ).join(
                ItemUnitConversion,
                (Item.kodeitem == ItemUnitConversion.kodeitem)
            ).outerjoin(
                ItemSellingPrice,
                (Item.kodeitem == ItemSellingPrice.kodeitem) &
                (ItemUnitConversion.satuan == ItemSellingPrice.satuan)
            )

            # Apply search filters
            for term in search_terms:
                query = query.filter(
                    func.lower(
                        Item.kodeitem + ' ' +
                        Item.namaitem + ' ' +
                        func.coalesce(Item.merek, '') + ' ' +
                        func.coalesce(Item.jenis, '') + ' ' +
                        func.coalesce(Item.keterangan, '') + ' ' +
                        func.coalesce(Item.supplier1, '')
                    ).contains(term.lower())
                )

            results = query.all()

            # Get current date for calculations
            current_date = datetime.now()
            one_year_ago = current_date - timedelta(days=365)

            # Calculate monthly sales for each item (last 12 months) - same logic as main route
            monthly_sales = {}
            for result in results:
                kodeitem = result.Item.kodeitem
                monthly_data = []
                has_sales = False

                # Get current month and year
                current_month = current_date.month
                current_year = current_date.year

                # Calculate sales for last 12 months
                for i in range(12):
                    month = current_month - i
                    year = current_year

                    if month <= 0:
                        month += 12
                        year -= 1

                    month_sales = get_monthly_sales_for_item(kodeitem, year, month, db, old_db)

                    if month_sales > 0:
                        has_sales = True
                    monthly_data.append(str(int(month_sales)))

                if has_sales:
                    monthly_data.reverse()
                    monthly_sales[kodeitem] = '-'.join(monthly_data)
                else:
                    monthly_sales[kodeitem] = ''

            # Calculate last year's 3-month sales for stock prediction - same logic as main route
            current_date = datetime.now()
            current_month = current_date.month
            current_year = current_date.year

            last_year = current_year - 1

            start_month = current_month
            start_year = last_year

            last_year_start = datetime(start_year, start_month, 1)

            end_month = start_month + 3
            end_year = start_year
            if end_month > 12:
                end_month -= 12
                end_year += 1
            last_year_end = datetime(end_year, end_month, 1)

            one_year_ago = current_date - timedelta(days=365)

            item_codes = [r.Item.kodeitem for r in results]

            # Check if current DB has complete data
            current_db_has_complete_data = {}
            for kodeitem in item_codes:
                months_with_data = 0
                current_month = current_date.month
                current_year = current_date.year

                for i in range(12):
                    month = current_month - i
                    year = current_year

                    if month <= 0:
                        month += 12
                        year -= 1

                    start_date = datetime(year, month, 1)
                    if month == 12:
                        end_date = datetime(year + 1, 1, 1)
                    else:
                        end_date = datetime(year, month + 1, 1)

                    month_data = db.query(ItemSalesHeader).filter(
                        ItemSalesHeader.tanggal >= start_date,
                        ItemSalesHeader.tanggal < end_date,
                        ItemSalesHeader.tipe == 'KSR'
                    ).join(
                        ItemSalesDetail,
                        ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
                    ).filter(
                        ItemSalesDetail.kodeitem == kodeitem
                    ).count()

                    if month_data > 0:
                        months_with_data += 1

                current_db_has_complete_data[kodeitem] = months_with_data >= 6

            # Get last year's 3-month sales
            last_year_sales_current = db.query(
                ItemSalesDetail.kodeitem,
                func.sum(ItemSalesDetail.jumlah).label('total_sales')
            ).join(
                ItemSalesHeader,
                (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
                (ItemSalesHeader.tipe == 'KSR')
            ).filter(
                ItemSalesHeader.tanggal >= last_year_start,
                ItemSalesHeader.tanggal < last_year_end,
                ItemSalesDetail.kodeitem.in_(item_codes)
            ).group_by(
                ItemSalesDetail.kodeitem
            ).all()

            items_needing_old_data = []
            current_db_last_year_items = {item.kodeitem for item in last_year_sales_current}
            items_needing_old_data = [kodeitem for kodeitem in item_codes
                                    if kodeitem not in current_db_last_year_items]

            last_year_sales_old = []
            if items_needing_old_data:
                last_year_sales_old = old_db.query(
                    ItemSalesDetail.kodeitem,
                    func.sum(ItemSalesDetail.jumlah).label('total_sales')
                ).join(
                    ItemSalesHeader,
                    (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
                    (ItemSalesHeader.tipe == 'KSR')
                ).filter(
                    ItemSalesHeader.tanggal >= last_year_start,
                    ItemSalesHeader.tanggal < last_year_end,
                    ItemSalesDetail.kodeitem.in_(items_needing_old_data)
                ).group_by(
                    ItemSalesDetail.kodeitem
                ).all()

            last_year_sales_dict = {}
            for item in last_year_sales_current:
                last_year_sales_dict[item.kodeitem] = item.total_sales or 0

            for item in last_year_sales_old:
                if item.kodeitem not in last_year_sales_dict:
                    last_year_sales_dict[item.kodeitem] = item.total_sales or 0

            # Get yearly sales
            yearly_sales_current = db.query(
                ItemSalesDetail.kodeitem,
                func.sum(ItemSalesDetail.jumlah).label('total_sales')
            ).join(
                ItemSalesHeader,
                (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
                (ItemSalesHeader.tipe == 'KSR')
            ).filter(
                ItemSalesHeader.tanggal >= one_year_ago,
                ItemSalesDetail.kodeitem.in_(item_codes)
            ).group_by(
                ItemSalesDetail.kodeitem
            ).all()

            items_needing_old_data = [kodeitem for kodeitem in item_codes
                                    if not current_db_has_complete_data.get(kodeitem, False)]

            yearly_sales_old = []
            if items_needing_old_data:
                yearly_sales_old = old_db.query(
                    ItemSalesDetail.kodeitem,
                    func.sum(ItemSalesDetail.jumlah).label('total_sales')
                ).join(
                    ItemSalesHeader,
                    (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
                    (ItemSalesHeader.tipe == 'KSR')
                ).filter(
                    ItemSalesHeader.tanggal >= one_year_ago,
                    ItemSalesDetail.kodeitem.in_(items_needing_old_data)
                ).group_by(
                    ItemSalesDetail.kodeitem
                ).all()

            sales_dict = {}
            for item in yearly_sales_current:
                sales_dict[item.kodeitem] = item.total_sales or 0

            for item in yearly_sales_old:
                if not current_db_has_complete_data.get(item.kodeitem, False):
                    if item.kodeitem not in sales_dict or sales_dict[item.kodeitem] == 0:
                        sales_dict[item.kodeitem] = item.total_sales or 0
                    else:
                        sales_dict[item.kodeitem] = item.total_sales or 0

            # Process results with proper unit conversion
            for result in results:
                kodeitem = result.Item.kodeitem

                # Calculate stock needed based on last year's same 3-month period
                last_year_3month_sale = last_year_sales_dict.get(kodeitem, 0)
                predicted_need = last_year_3month_sale  # Use last year's same period to predict this year's need

                # Get base stock (always in base unit)
                current_stock = result.stok if result.stok is not None else 0

                # Calculate if stock is low (always compare in base units)
                is_low_stock = current_stock <= result.Item.stokmin

                # Convert stock for display if needed
                converted_stock = current_stock
                if hasattr(result, 'tipe') and result.tipe == 'K' and hasattr(result, 'jumlahkonv') and result.jumlahkonv:
                    converted_stock = current_stock / result.jumlahkonv

                needed = predicted_need - converted_stock  # ❌ WRONG: Using converted_stock instead of current_stock
                stock_needed = round(needed, 2) if needed > 0 else None

                items_data.append({
                    'kodeitem': result.Item.kodeitem,
                    'namaitem': result.Item.namaitem,
                    'stok': converted_stock,
                    'base_stock': current_stock,
                    'is_low_stock': is_low_stock,
                    'last_12months_sales': monthly_sales.get(kodeitem, ''),
                    'stock_needed_3month': stock_needed,
                    'hargapokok': result.hargapokok,
                    'hargajual': result.hargajual,
                    'satuan': result.satuan,
                    'tipe': result.tipe,
                    'merek': result.Item.merek or '',
                    'jenis': result.Item.jenis or '',
                    'rak': result.Item.rak or '',
                    'keterangan': result.Item.keterangan or '',
                    'supplier1': result.Item.supplier1 or ''
                })

            # Apply distinct filter if enabled
            is_distinct = distinct == 'true'
            if is_distinct:
                unique_items = {}
                for item in items_data:
                    kodeitem = item['kodeitem']
                    if kodeitem not in unique_items:
                        unique_items[kodeitem] = item
                items_data = list(unique_items.values())

        finally:
            old_db.close()

    # Create DataFrame for Excel export
    if items_data:
        df = pd.DataFrame(items_data)

        # Clean column names for Excel
        df.columns = ['Kode Item', 'Nama Item', 'Stok', 'Base Stock', 'Low Stock',
                     'Last 12M Sales', 'Needed (3M)', 'HPP', 'Harga Jual', 'Satuan',
                     'Tipe', 'Merek', 'Jenis', 'Rak', 'Keterangan', 'Supplier']

        # Add low stock indicator column after Last 12M Sales
        df['Low Stock Indicator'] = df.apply(lambda row: '*' if row['Low Stock'] else '', axis=1)

        # Reorder columns to match table display order with new column
        column_order = ['Kode Item', 'Nama Item', 'Stok', 'Needed (3M)', 'Last 12M Sales',
                       'Low Stock Indicator', 'Merek', 'Jenis', 'HPP', 'Harga Jual', 'Rak', 'Keterangan', 'Supplier']
        df = df[column_order]

        # Sort by Kode Item
        df = df.sort_values('Kode Item')

    # Generate filename
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    if search:
        # Clean search text for filename (remove special characters, replace spaces with underscore)
        clean_search = re.sub(r'[^\w\s-]', '', search).strip().replace(' ', '_')
        if len(clean_search) > 50:  # Limit filename length
            clean_search = clean_search[:50]
        filename = f"{clean_search}_12months_{current_time}.xlsx"
    else:
        filename = f"all_items_12months_{current_time}.xlsx"

    # Create Excel file in memory
    output = io.BytesIO()
    if items_data:
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='12 Month Sales', index=False)

            # Get the workbook and worksheet
            workbook = writer.book
            worksheet = writer.sheets['12 Month Sales']

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter

                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = min(max_length + 2, 50)  # Cap at 50
                worksheet.column_dimensions[column_letter].width = adjusted_width

    output.seek(0)

    # Return Excel file for direct download
    headers = {
        'Content-Disposition': f'attachment; filename="{filename}"',
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Length': str(len(output.getvalue())),
        'Cache-Control': 'no-cache'
    }

    return Response(
        content=output.getvalue(),
        headers=headers,
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )




