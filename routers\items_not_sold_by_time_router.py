from fastapi import API<PERSON>outer, Request, Depends, HTTPException, Query
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from datetime import datetime, timedelta
from database.database_connection import get_db
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, or_, text
from database.database_model import (
    Item,
    ItemSalesHeader,
    ItemSalesDetail,
    ItemStock,
    ItemSellingPrice
)

router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/items-not-sold-by-time", response_class=HTMLResponse)
async def items_not_sold_by_time(
    request: Request, 
    days: int = Query(None, description="Number of days to check for items not sold"),
    db: Session = Depends(get_db)
):
    """
    Display items that have not been sold within the specified number of days.
    Default is 365 days (1 year) if no days parameter is provided.
    Queries both current and old databases when the date range exceeds current DB coverage.
    """
    
    # If days is provided, perform the search
    items = []
    if days is not None:
        try:
            # Calculate the cutoff date
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Determine if we need to query the old database
            # Assume current DB holds approximately 100 days of data
            current_db_coverage_days = 100
            need_old_db = days > current_db_coverage_days
            
            # Calculate date ranges for better query optimization
            current_db_cutoff = datetime.now() - timedelta(days=current_db_coverage_days)
            
            # Get items from current database
            current_items = get_items_not_sold_from_db(db, cutoff_date, days)
            items.extend(current_items)
            
            # If we need data from older periods, query the old database
            if need_old_db:
                # Get old database session
                old_db_gen = get_db("old")
                old_db = next(old_db_gen)
                try:
                    # For old database, we want items that haven't been sold since the cutoff_date
                    # but we also need to check if they were sold in the current database period
                    old_items = get_items_not_sold_from_db(old_db, cutoff_date, days)
                    
                    # Get all items that HAVE been sold in the current database within the period
                    current_sold_items = get_sold_items_in_period(db, cutoff_date)
                    
                    # Merge items, avoiding duplicates and ensuring comprehensive coverage
                    existing_codes = {item['kode'] for item in items}
                    for old_item in old_items:
                        # Skip if item already exists in current results
                        if old_item['kode'] in existing_codes:
                            continue
                            
                        # Skip if item was sold in current database within the period
                        if old_item['kode'] in current_sold_items:
                            continue
                            
                        # Check if this item was sold in the current database period
                        # by getting its most recent sale date from both databases
                        current_last_sale = get_last_sale_date(db, old_item['kode'])
                        old_last_sale = old_item['last_sale_date']
                        
                        # Use the most recent sale date
                        if current_last_sale != 'Never' and current_last_sale != 'Error':
                            old_item['last_sale_date'] = current_last_sale
                        
                        items.append(old_item)
                finally:
                    old_db.close()
            
        except Exception as e:
            print(f"Error in items_not_sold_by_time: {e}")
            import traceback
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    
    return templates.TemplateResponse(
        "items_not_sold_by_time.html",
        {
            "request": request,
            "items": items if days is not None else [],
            "days": days
        }
    )

def get_items_not_sold_from_db(db: Session, cutoff_date: datetime, days: int):
    """
    Get items that have not been sold within the specified period from a database session.
    Returns a list of item dictionaries.
    """
    items = []
    
    # Get all items with their basic information
    items_query = db.query(
        Item.kodeitem.label('kode'),
        Item.namaitem.label('nama'),
        ItemStock.stok,
        Item.merek,
        Item.jenis,
        Item.rak,
        Item.keterangan,
        Item.supplier1.label('supplier'),
        ItemSellingPrice.hargajual.label('harga')
    ).outerjoin(
        ItemStock,
        Item.kodeitem == ItemStock.kodeitem
    ).outerjoin(
        ItemSellingPrice,
        and_(
            Item.kodeitem == ItemSellingPrice.kodeitem,
            ItemSellingPrice.satuan == Item.satuan  # Match on base unit
        )
    )
    
    # Get items that have been sold within the specified period
    sold_items_subquery = db.query(
        ItemSalesDetail.kodeitem
    ).join(
        ItemSalesHeader,
        and_(
            ItemSalesDetail.notransaksi == ItemSalesHeader.notransaksi,
            ItemSalesHeader.tanggal >= cutoff_date,
            ItemSalesHeader.tipe == 'KSR'  # Only include actual sales
        )
    ).distinct().subquery()
    
    # Filter out items that have been sold within the period
    items_query = items_query.filter(
        ~Item.kodeitem.in_(
            db.query(sold_items_subquery.c.kodeitem)
        )
    ).order_by(Item.namaitem)
    
    results = items_query.all()
    
    # For each item, get the last sale date
    for result in results:
        last_sale_date = get_last_sale_date(db, result.kode)
        
        items.append({
            'kode': result.kode,
            'nama': result.nama,
            'stok': result.stok if result.stok is not None else 0,
            'merek': result.merek,
            'jenis': result.jenis,
            'harga': result.harga,
            'rak': result.rak,
            'keterangan': result.keterangan,
            'supplier': result.supplier,
            'last_sale_date': last_sale_date
        })
    
    return items

def get_sold_items_in_period(db: Session, cutoff_date: datetime):
    """
    Get a set of item codes that have been sold within the specified period.
    """
    sold_items = db.query(
        ItemSalesDetail.kodeitem
    ).join(
        ItemSalesHeader,
        and_(
            ItemSalesDetail.notransaksi == ItemSalesHeader.notransaksi,
            ItemSalesHeader.tanggal >= cutoff_date,
            ItemSalesHeader.tipe == 'KSR'  # Only include actual sales
        )
    ).distinct().all()
    
    return {item.kodeitem for item in sold_items}

def get_last_sale_date(db: Session, item_code: str):
    """
    Get the last sale date for a specific item.
    Returns the date as a string or 'Never' if no sales found.
    """
    try:
        last_sale = db.query(
            ItemSalesHeader.tanggal
        ).join(
            ItemSalesDetail,
            ItemSalesDetail.notransaksi == ItemSalesHeader.notransaksi
        ).filter(
            and_(
                ItemSalesDetail.kodeitem == item_code,
                ItemSalesHeader.tipe == 'KSR'  # Only actual sales
            )
        ).order_by(ItemSalesHeader.tanggal.desc()).first()
        
        if last_sale and last_sale.tanggal:
            return last_sale.tanggal.strftime('%Y-%m-%d')
        else:
            return 'Never'
    except Exception as e:
        print(f"Error getting last sale date for {item_code}: {e}")
        return 'Error'