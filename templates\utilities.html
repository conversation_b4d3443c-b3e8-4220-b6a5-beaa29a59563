<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Utilities</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            backgroundColor: {
              "gray-750": "#2d3748",
              "gray-850": "#1a1a1a",
              "gray-880": "#242424",
              "gray-900": "#1e293b",
            },
          },
        },
      };
    </script>
  </head>
  <body class="bg-black text-gray-100">
    <div class="min-h-screen">
      <!-- Navbar -->
      <nav class="bg-gray-800 shadow-lg sticky top-0 z-50">
        <div class="max-w-[95%] mx-auto px-4">
          <div class="flex justify-between h-16">
            <div class="flex items-center space-x-4">
              <h1 class="text-3xl font-bold text-white">Utilities</h1>
            </div>
            <div class="flex items-center">
              <button
                onclick="window.close()"
                class="px-6 py-2 text-lg bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-150"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-[95%] mx-auto px-4 py-8">
        <div class="bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 class="text-2xl font-semibold mb-4">Utilities Page</h2>
          <p class="text-gray-300 mb-4">
            This is the utilities page. Content will be implemented later.
          </p>

          <!-- Utilities Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Log Checker Utility -->
            <div class="bg-gray-700 rounded-lg p-4">
              <h3 class="text-lg font-medium mb-2">Playwright Log Checker</h3>
              <p class="text-gray-400 mb-4">
                Check the latest log date and view last 10 lines from
                playwright_log.txt
              </p>
              <button
                onclick="checkLog()"
                class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-150"
                id="checkLogBtn"
              >
                Check Log
              </button>
              <div id="logResults" class="mt-4 hidden">
                <div class="bg-gray-800 rounded-lg p-3">
                  <div id="logStatus" class="mb-2"></div>
                  <div id="logContent"></div>
                </div>
              </div>
            </div>

            <!-- Open Log File Utility -->
            <div class="bg-gray-700 rounded-lg p-4">
              <h3 class="text-lg font-medium mb-2">Open Log File</h3>
              <p class="text-gray-400 mb-4">
                Open playwright_log.txt with the default application
              </p>
              <button
                onclick="openLogFile()"
                class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-150"
                id="openLogBtn"
              >
                Open Log File
              </button>
              <div id="openLogStatus" class="mt-4 hidden">
                <div class="bg-gray-800 rounded-lg p-3">
                  <div id="openLogMessage"></div>
                </div>
              </div>
            </div>
            <div class="bg-gray-700 rounded-lg p-4">
              <h3 class="text-lg font-medium mb-2">Bought Item from Marketplace</h3>
              <p class="text-gray-400 mb-4">Track items purchased from marketplace with delivery status</p>
              <button onclick="window.location.href='/marketplace-purchases'" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors w-full">
                <i class="fas fa-shopping-cart mr-2"></i>Record Purchases
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      async function checkLog() {
        const btn = document.getElementById("checkLogBtn");
        const resultsDiv = document.getElementById("logResults");
        const statusDiv = document.getElementById("logStatus");
        const contentDiv = document.getElementById("logContent");

        // Show loading state
        btn.disabled = true;
        btn.textContent = "Checking...";
        resultsDiv.classList.remove("hidden");
        statusDiv.innerHTML =
          '<div class="text-yellow-400">🔄 Checking log file...</div>';
        contentDiv.innerHTML = "";

        try {
          const response = await fetch("/check-log");
          const data = await response.json();

          if (data.success) {
            // Compare timestamps to determine container color
            let containerClass = "bg-gray-800";
            if (data.latest_error && data.latest_success_message) {
              // Extract timestamps from both messages
              const errorMatch = data.latest_error.match(
                /(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/
              );
              const successMatch = data.latest_success_message.match(
                /(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/
              );

              if (errorMatch && successMatch) {
                const errorTime = new Date(errorMatch[1]);
                const successTime = new Date(successMatch[1]);

                if (errorTime > successTime) {
                  containerClass = "bg-red-900/30 border border-red-700/50";
                } else {
                  containerClass = "bg-green-900/30 border border-green-700/50";
                }
              }
            } else if (data.latest_error && !data.latest_success_message) {
              containerClass = "bg-red-900/30 border border-red-700/50";
            } else if (!data.latest_error && data.latest_success_message) {
              containerClass = "bg-green-900/30 border border-green-700/50";
            }

            // Apply the container class to the results div
            resultsDiv.className = resultsDiv.className
              .replace("hidden", "")
              .trim();
            const logContentDiv = resultsDiv.querySelector(".bg-gray-800");
            if (logContentDiv) {
              logContentDiv.className = logContentDiv.className.replace(
                "bg-gray-800",
                containerClass
              );
            }

            // Show success status
            statusDiv.innerHTML = `
                        <div class="text-green-400 mb-2">✅ ${
                          data.message
                        }</div>
                        <div class="text-sm text-gray-300">
                            <strong>Latest Date Found:</strong> ${
                              data.latest_date || "No date found"
                            }<br>
                            <strong>Total Lines:</strong> ${
                              data.total_lines
                            }<br>
                            <strong>File Path:</strong> ${data.file_path}
                        </div>
                    `;

            // Show latest error if found
            let errorSection = "";
            if (data.latest_error) {
              errorSection = `
                            <div class="mt-3">
                                <h4 class="text-sm font-medium text-red-400 mb-2">Latest Error Log:</h4>
                                <div class="bg-red-900/20 border border-red-700/50 rounded p-3 text-xs font-mono overflow-x-auto">
                                    <pre class="text-red-300 whitespace-pre-wrap">${data.latest_error}</pre>
                                </div>
                            </div>
                        `;
            }

            // Show latest success message
            let successSection = "";
            if (data.latest_success_message) {
              successSection = `
                            <div class="mt-3">
                                <h4 class="text-sm font-medium text-green-400 mb-2">Latest Success Message:</h4>
                                <div class="bg-green-900/20 border border-green-700/50 rounded p-3 text-xs font-mono overflow-x-auto">
                                    <pre class="text-green-300 whitespace-pre-wrap">${data.latest_success_message}</pre>
                                </div>
                            </div>
                        `;
            }

            // Show latest session run
            let sessionSection = "";
            if (data.latest_session_run) {
              sessionSection = `
                            <div class="mt-3">
                                <h4 class="text-sm font-medium text-blue-400 mb-2">Latest Session Run:</h4>
                                <div class="bg-blue-900/20 border border-blue-700/50 rounded p-3 text-xs font-mono overflow-x-auto">
                                    <pre class="text-blue-300 whitespace-pre-wrap">${data.latest_session_run}</pre>
                                </div>
                            </div>
                        `;
            }

            // Combine all sections
            contentDiv.innerHTML =
              errorSection + successSection + sessionSection ||
              '<div class="text-gray-400 mt-2">No content to display</div>';
          } else {
            // Show error status
            statusDiv.innerHTML = `<div class="text-red-400">❌ ${data.message}</div>`;
            if (data.path) {
              contentDiv.innerHTML = `<div class="text-sm text-gray-400 mt-2">Path: ${data.path}</div>`;
            }
          }
        } catch (error) {
          statusDiv.innerHTML = `<div class="text-red-400">❌ Error: ${error.message}</div>`;
          contentDiv.innerHTML = "";
        } finally {
          // Reset button
          btn.disabled = false;
          btn.textContent = "Check Log";
        }
      }

      async function openLogFile() {
        const btn = document.getElementById("openLogBtn");
        const statusDiv = document.getElementById("openLogStatus");
        const messageDiv = document.getElementById("openLogMessage");

        // Show loading state
        btn.disabled = true;
        btn.textContent = "Opening...";
        statusDiv.classList.remove("hidden");
        messageDiv.innerHTML =
          '<div class="text-yellow-400">🔄 Opening log file...</div>';

        try {
          const response = await fetch("/open-log-file", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
          });
          const data = await response.json();

          if (data.success) {
            messageDiv.innerHTML = `
                        <div class="text-green-400">✅ ${data.message}</div>
                        <div class="text-sm text-gray-300 mt-1">File: ${data.file_path}</div>
                    `;

            // Hide status after 3 seconds
            setTimeout(() => {
              statusDiv.classList.add("hidden");
            }, 3000);
          } else {
            messageDiv.innerHTML = `
                        <div class="text-red-400">❌ ${data.message}</div>
                        ${
                          data.path
                            ? `<div class="text-sm text-gray-400 mt-1">Path: ${data.path}</div>`
                            : ""
                        }
                    `;
          }
        } catch (error) {
          messageDiv.innerHTML = `<div class="text-red-400">❌ Error: ${error.message}</div>`;
        } finally {
          // Reset button
          btn.disabled = false;
          btn.textContent = "Open Log File";
        }
      }
    </script>
  </body>
</html>
