<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Items Not Sold by Time</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            backgroundColor: {
              "gray-750": "#2d3748",
              "gray-850": "#1a1a1a",
              "gray-880": "#242424",
              "gray-900": "#1e293b",
            },
          },
        },
      };
    </script>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css"
    />
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
  </head>
  <body class="bg-black text-gray-100">
    <div class="min-h-screen">
      <!-- Navbar -->
      <nav class="bg-gray-800 shadow-lg sticky top-0 z-50">
        <div class="max-w-[95%] mx-auto px-4">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <h1 class="text-3xl font-bold text-white">
                Items Not Sold by Time
              </h1>
            </div>
            <div class="flex items-center space-x-4">
              <button
                onclick="exportToExcel()"
                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-150"
              >
                Export to Excel
              </button>
              <a
                href="/"
                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-150"
              >
                Back to Home
              </a>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-[95%] mx-auto px-4 py-6">
        <!-- Search Form -->
        <div class="mb-6 bg-gray-800 rounded-lg p-6">
          <form method="GET" class="flex items-center space-x-4">
            <div class="flex-1">
              <label for="days" class="block text-sm font-medium text-gray-300 mb-2">
                Days since last sale:
              </label>
              <input
                type="number"
                id="days"
                name="days"
                value="{{ days or 365 }}"
                min="1"
                max="3650"
                class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter number of days (default: 365)"
              />
            </div>
            <div class="flex-shrink-0">
              <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-150 mt-6"
              >
                Search
              </button>
            </div>
          </form>
        </div>

        <!-- Results -->
        {% if items is defined and items is not none %}
        <div class="mb-4">
          <span class="text-lg">Items not sold in the last {{ days }} days: </span>
          <span class="text-lg font-bold text-red-400">{{ items|length }}</span>
        </div>

        <!-- Table -->
        <div class="overflow-x-auto">
          <table class="w-full bg-gray-800 rounded-lg overflow-hidden">
            <thead class="bg-gray-700">
              <tr>
                <th class="px-2 py-2 text-left text-xs">Kode</th>
                <th class="px-2 py-2 text-left text-xs">Nama</th>
                <th class="px-2 py-2 text-center text-xs">Stok</th>
                <th class="px-2 py-2 text-left text-xs">Merek</th>
                <th class="px-2 py-2 text-left text-xs">Jenis</th>
                <th class="px-2 py-2 text-right text-xs">Harga</th>
                <th class="px-2 py-2 text-left text-xs">Rak</th>
                <th class="px-2 py-2 text-left text-xs">Keterangan</th>
                <th class="px-2 py-2 text-left text-xs">Supplier</th>
                <th class="px-2 py-2 text-left text-xs">Last Sale</th>
              </tr>
            </thead>
            <tbody class="bg-black divide-y divide-gray-700">
              {% for item in items %}
              <tr class="hover:bg-gray-700 transition duration-150">
                <td class="px-2 py-1 text-xs text-gray-300" style="max-width: 120px;">
                  <a
                    href="/item-history/{{ item.kode }}"
                    target="_blank"
                    class="hover:text-blue-400 transition-colors duration-150"
                    title="{{ item.kode }}"
                  >
                    {{ item.kode[:15] }}{% if item.kode|length > 15 %}...{% endif %}
                  </a>
                </td>
                <td class="px-2 py-1 text-xs" style="max-width: 320px;">
                  <span class="text-white" title="{{ item.nama }}">{{ item.nama[:40] }}{% if item.nama|length > 40 %}...{% endif %}</span>
                  <button
                    class="ml-1 text-gray-400 hover:text-white"
                    onclick="copyToClipboard('{{ item.nama }}'); event.stopPropagation();"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-3 w-3"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                      />
                    </svg>
                  </button>
                </td>
                <td class="px-2 py-1 text-center text-xs">
                  <span class="{% if item.stok <= 0 %}text-red-400{% elif item.stok <= 5 %}text-yellow-400{% else %}text-green-400{% endif %}">
                    {{ item.stok }}
                  </span>
                </td>
                <td class="px-2 py-1 text-xs text-gray-300" style="max-width: 120px;" title="{{ item.merek or '-' }}">
                  {{ (item.merek or '-')[:15] }}{% if item.merek and item.merek|length > 15 %}...{% endif %}
                </td>
                <td class="px-2 py-1 text-xs text-gray-300" style="max-width: 120px;" title="{{ item.jenis or '-' }}">
                  {{ (item.jenis or '-')[:15] }}{% if item.jenis and item.jenis|length > 15 %}...{% endif %}
                </td>
                <td class="px-2 py-1 text-right text-xs">
                  {% if item.harga %}
                    {{ "{:,.0f}".format(item.harga) }}
                  {% else %}
                    -
                  {% endif %}
                </td>
                <td class="px-2 py-1 text-xs text-gray-300" style="max-width: 120px;" title="{{ item.rak or '-' }}">
                  {{ (item.rak or '-')[:15] }}{% if item.rak and item.rak|length > 15 %}...{% endif %}
                </td>
                <td class="px-2 py-1 text-xs text-gray-300" style="max-width: 400px;" title="{{ item.keterangan or '-' }}">
                  {{ (item.keterangan or '-')[:50] }}{% if item.keterangan and item.keterangan|length > 50 %}...{% endif %}
                </td>
                <td class="px-2 py-1 text-xs text-gray-300" style="max-width: 120px;" title="{{ item.supplier or '-' }}">
                  {{ (item.supplier or '-')[:15] }}{% if item.supplier and item.supplier|length > 15 %}...{% endif %}
                </td>
                <td class="px-2 py-1 text-xs text-gray-300">
                  {{ item.last_sale_date or 'Never' }}
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        {% else %}
        <div class="text-center py-8 text-gray-400">
          <p class="text-xl">Enter the number of days to search for items not sold</p>
          <p class="text-sm mt-2">
            Default is 365 days (1 year). Use the form above to search.
          </p>
        </div>
        {% endif %}
      </div>
    </div>

    <script>
      function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
          Toastify({
            text: "Copied to clipboard: " + text,
            duration: 2000,
            gravity: "top",
            position: "right",
            backgroundColor: "linear-gradient(to right, #00b09b, #96c93d)",
          }).showToast();
        });
      }

      async function exportToExcel() {
        try {
          const days = document.getElementById('days').value || 365;
          const response = await fetch(`/export/items-not-sold-by-time?days=${days}`);
          
          if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `items_not_sold_${days}_days.xlsx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            
            Toastify({
              text: "Excel file downloaded successfully!",
              duration: 3000,
              gravity: "top",
              position: "right",
              backgroundColor: "linear-gradient(to right, #00b09b, #96c93d)",
            }).showToast();
          } else {
            throw new Error('Export failed');
          }
        } catch (error) {
          console.error('Export error:', error);
          Toastify({
            text: "Failed to export Excel file",
            duration: 3000,
            gravity: "top",
            position: "right",
            backgroundColor: "linear-gradient(to right, #ff5f6d, #ffc371)",
          }).showToast();
        }
      }
    </script>
  </body>
</html>