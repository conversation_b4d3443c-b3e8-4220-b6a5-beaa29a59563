<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Today Low Stock Items</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            backgroundColor: {
              "gray-750": "#2d3748",
              "gray-850": "#1a1a1a",
              "gray-880": "#242424",
              "gray-900": "#1e293b",
            },
          },
        },
      };
    </script>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css"
    />
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <!-- Include today-sales-details.js with defer to ensure it loads properly -->
    <script src="/static/js/today-sales-details.js" defer></script>
  </head>
  <body class="bg-black text-gray-100">
    <div class="min-h-screen">
      <!-- Navbar -->
      <nav class="bg-gray-800 shadow-lg sticky top-0 z-50">
        <div class="max-w-[95%] mx-auto px-4">
            <div class="flex h-16">
              <div class="flex items-center space-x-4">
                <h1 class="text-3xl font-bold text-white">
                  Today Low Stock Items
                </h1>
                <!-- Date Navigation -->
                <div
                  class="flex items-center space-x-2 bg-gray-700 rounded-lg px-4 py-2"
                >
                  <a
                    href="/low-stock-items?date={{ prev_date }}"
                    class="text-gray-300 hover:text-white transition-colors duration-150"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </a>
                  <input
                    type="date"
                    id="dateSelector"
                    value="{{ current_date }}"
                    class="bg-transparent text-xl text-center focus:outline-none"
                    onchange="window.location.href = '/low-stock-items?date=' + this.value"
                  />
                  <a
                    href="/low-stock-items?date={{ next_date }}"
                    class="text-gray-300 hover:text-white transition-colors duration-150"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </a>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <button
                  onclick="openThreeMonthModal()"
                  class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                  3-Month
                </button>
                <button
                  onclick="window.location.reload()"
                  class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                  Refresh
                </button>
                <button
                  onclick="sendReport()"
                  class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center"
                >
                  Send Report
                </button>
                <button
                  onclick="window.close()"
                  class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center"
                >
                  Close
                </button>
              </div>
            </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-[95%] mx-auto px-4 py-6">
        <div class="bg-gray-800 rounded-lg p-6">
          <!-- Items Count -->
          <div class="mb-4">
            <span class="text-lg">Items Sold Today with Low Stock: </span>
            <span class="text-lg font-bold text-yellow-400"
              >{{ items|length }}</span
            >
          </div>
          <!-- Table -->
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-700">
                <tr>
                  <th class="px-4 py-3 text-left">Code</th>
                  <th class="px-4 py-3 text-left">Name</th>
                  <th class="px-4 py-3 text-right">Price</th>
                  <th class="px-4 py-3 text-left">Details</th>
                </tr>
              </thead>
              <tbody class="bg-black divide-y divide-gray-700">
                {% for item in items %}
                <tr
                  class="hover:bg-gray-700 transition duration-150"
                  data-last-order-date="{{ item.last_order_date or '' }}"
                  data-has-recent-purchase="{{ item.has_recent_purchase|lower }}"
                >
                  <td
                    class="px-3 py-1 whitespace-nowrap text-base text-gray-300"
                  >
                    <a
                      href="/item-history/{{ item.code }}"
                      target="_blank"
                      class="hover:text-blue-400 transition-colors duration-150"
                      >{{ item.code }}</a
                    >
                  </td>
                  <td class="px-3 py-1 whitespace-nowrap text-base">
                    {% set parts = item.name.split(' - ', 2) %}
                    <span
                      class="text-white cursor-pointer item-name-part search-item-name"
                      data-item-name="{{ parts[1] }}"
                      data-item-code="{{ item.code }}"
                      >{{ parts[0] }}</span
                    >
                    <span class="text-gray-300"> - </span>
                    <span
                      class="text-white cursor-pointer item-name-part search-item-name"
                      data-item-name="{{ parts[1] }}"
                      data-item-code="{{ item.code }}"
                      >{{ parts[1] }}</span
                    >
                    <span class="text-gray-300"> - </span>
                    <span class="text-red-400">{{ parts[2] }}</span>
                    <button
                      class="ml-1 text-gray-400 hover:text-white"
                      onclick="copyToClipboard('{{ parts[1] }}'); event.stopPropagation();"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                        />
                      </svg>
                    </button>
                  </td>
                  <td class="px-4 py-3 text-right whitespace-nowrap">
                    {{ item.price | currency }}
                  </td>
                  <td class="px-4 py-3 whitespace-nowrap">
                    {{ item.details }}
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Three Month Sales Modal -->
    <div
      id="threeMonthModal"
      class="fixed inset-0 bg-gray-900 bg-opacity-70 hidden items-center justify-center z-50"
    >
      <div
        class="bg-gray-800 rounded-lg shadow-xl border-2 border-blue-500 w-[75%] h-[75vh] max-h-[75vh] flex flex-col"
      >
        <div
          class="flex justify-between items-center p-4 border-b border-gray-700"
        >
          <h3 class="text-xl font-bold text-white">3-Month Sales</h3>
          <button
            onclick="closeThreeMonthModal()"
            class="text-gray-400 hover:text-white"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <div class="flex-grow overflow-hidden p-4">
          <iframe
            id="threeMonthFrame"
            src=""
            class="w-full h-full border-none"
          ></iframe>
        </div>
      </div>
    </div>
  </body>
</html>
<script>
  // Add copy on select functionality
  document.addEventListener("mouseup", function () {
    const selectedText = window.getSelection().toString().trim();
    if (selectedText) {
      navigator.clipboard.writeText(selectedText).catch((err) => {
        console.error("Failed to copy text: ", err);
      });
    }
  });

  async function sendReport() {
    const urlParams = new URLSearchParams(window.location.search);
    const date = urlParams.get("date") || "";

    try {
      const response = await fetch(`/send-report?date=${date}`);
      const data = await response.json();

      if (data.status === "success") {
        Toastify({
          text: "Report sent successfully to Telegram",
          duration: 3000,
          gravity: "top",
          position: "right",
          style: {
            background: "#22c55e", // green-600
          },
        }).showToast();
      } else {
        Toastify({
          text: data.message,
          duration: 3000,
          gravity: "top",
          position: "right",
          style: {
            background: "#ef4444", // red-600
          },
        }).showToast();
      }
    } catch (error) {
      Toastify({
        text: "Failed to send report",
        duration: 3000,
        gravity: "top",
        position: "right",
        style: {
          background: "#ef4444", // red-600
        },
      }).showToast();
      console.error("Error:", error);
    }
  }

  function copyToClipboard(text) {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        // Show a toast notification in the middle right
        Toastify({
          text: "Product name copied!",
          duration: 2000,
          gravity: "center",
          position: "right",
          style: {
            background: "#22c55e", // green-600
          },
        }).showToast();
      })
      .catch((err) => {
        console.error("Failed to copy text: ", err);
        // Show error toast notification
        Toastify({
          text: "Failed to copy text",
          duration: 2000,
          gravity: "center",
          position: "right",
          style: {
            background: "#ef4444", // red-600
          },
        }).showToast();
      });
  }

  // Chat history functionality is now handled by today-sales-details.js

  // Apply color coding based on last order date (same logic as chat history modal)
  function applyItemAgeColors() {
    const rows = document.querySelectorAll("tbody tr[data-last-order-date]");
    const today = new Date();

    rows.forEach((row) => {
      const lastOrderDate = row.getAttribute("data-last-order-date");
      const hasRecentPurchase =
        row.getAttribute("data-has-recent-purchase") === "true";

      // Get the item name parts (first two spans with item-name-part class)
      const itemNameParts = row.querySelectorAll(".item-name-part");

      // Determine color based on age (same logic as chat history modal)
      let colorClass = "text-white"; // Default color for recent orders (≤30 days)

      if (!lastOrderDate) {
        // No order history found - use red to indicate very old/no orders
        colorClass = "text-red-400";
      } else {
        const orderDate = new Date(lastOrderDate);
        const diffTime = Math.abs(today - orderDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays > 60) {
          colorClass = "text-red-400"; // Red for > 60 days since last order
        } else if (diffDays > 30) {
          colorClass = "text-yellow-400"; // Yellow for > 30 days since last order
        }
        // else remains white for ≤30 days since last order
      }

      // Apply color and strikethrough to item name parts
      itemNameParts.forEach((part) => {
        // Remove existing color classes and strikethrough
        part.classList.remove(
          "text-white",
          "text-yellow-400",
          "text-red-400",
          "text-gray-300",
          "line-through"
        );

        // Add new color class
        part.classList.add(colorClass);

        // Add strikethrough if there are recent purchases (BL or IM) - same logic as modal
        if (hasRecentPurchase) {
          part.classList.add("line-through");
        }
      });
    });
  }

  // Apply colors when page loads
  document.addEventListener("DOMContentLoaded", applyItemAgeColors);

  // Three Month Sales Modal Functions
  function openThreeMonthModal() {
    const modal = document.getElementById("threeMonthModal");
    const iframe = document.getElementById("threeMonthFrame");

    // Set the iframe source to the three-month sales page
    iframe.src = "/three-month-sales";

    // Show the modal
    modal.classList.remove("hidden");
    modal.classList.add("flex");

    // Prevent scrolling on the body
    document.body.style.overflow = "hidden";
  }

  function closeThreeMonthModal() {
    const modal = document.getElementById("threeMonthModal");
    const iframe = document.getElementById("threeMonthFrame");

    // Hide the modal
    modal.classList.add("hidden");
    modal.classList.remove("flex");

    // Clear the iframe source to stop any ongoing processes
    setTimeout(() => {
      iframe.src = "";
    }, 100);

    // Re-enable scrolling on the body
    document.body.style.overflow = "";
  }

  // Initialize modal click-outside behavior and chat history event listeners
  document.addEventListener("DOMContentLoaded", function () {
    // Add click event listener to close three-month modal when clicking outside
    const threeMonthModal = document.getElementById("threeMonthModal");
    if (threeMonthModal) {
      threeMonthModal.addEventListener("click", function (e) {
        if (e.target === this) {
          closeThreeMonthModal();
        }
      });
    }

    // Initialize chat history event listeners from today-sales-details.js
    if (typeof setupChatHistoryEventListeners === "function") {
      setupChatHistoryEventListeners();
    } else {
      console.error("setupChatHistoryEventListeners function not found");
    }
  });
</script>
