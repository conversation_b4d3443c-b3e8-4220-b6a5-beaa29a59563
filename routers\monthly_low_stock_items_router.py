from fastapi import APIRouter, Request, Depends, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
import aiohttp
from datetime import datetime
from database.database_connection import get_db
from sqlalchemy.orm import Session
from sqlalchemy import and_, func
from database.database_model import (
    Item,
    ItemSalesHeader,
    ItemSalesDetail,
    ItemStock,
    ItemSellingPrice
)
import calendar

router = APIRouter()
templates = Jinja2Templates(directory="templates")

# Add currency filter
def currency_filter(value):
    try:
        return f"Rp {int(value):,}"
    except (ValueError, TypeError):
        return "Rp 0"

# Register the currency filter
templates.env.filters["currency"] = currency_filter

@router.get("/monthly-low-stock-items", response_class=HTMLResponse)
async def monthly_low_stock_items(
    request: Request, 
    month: int = None, 
    year: int = None, 
    db: Session = Depends(get_db)
):
    current_date = datetime.now()
    year = year or current_date.year
    month = month or current_date.month
    
    # Calculate previous and next month/year
    if month == 1:
        prev_month, prev_year = 12, year - 1
    else:
        prev_month, prev_year = month - 1, year
        
    if month == 12:
        next_month, next_year = 1, year + 1
    else:
        next_month, next_year = month + 1, year

    # Function to build the query for a given database session
    def build_query(db_session):
        return db_session.query(
            Item.kodeitem.label('code'),
            Item.namaitem.label('name'),
            ItemSellingPrice.hargajual.label('price'),
            ItemStock.stok,
            Item.stokmin.label('stokmin'),
            Item.keterangan.label('detail'),
            func.sum(ItemSalesDetail.jumlah).label('total_sold')
        ).join(
            ItemSalesDetail,
            Item.kodeitem == ItemSalesDetail.kodeitem
        ).join(
            ItemSalesHeader,
            and_(
                ItemSalesDetail.notransaksi == ItemSalesHeader.notransaksi,
                func.extract('year', ItemSalesHeader.tanggal) == year,
                func.extract('month', ItemSalesHeader.tanggal) == month,
                ItemSalesHeader.tipe == 'KSR'
            )
        ).join(
            ItemStock,
            Item.kodeitem == ItemStock.kodeitem
        ).outerjoin(
            ItemSellingPrice,
            and_(
                Item.kodeitem == ItemSellingPrice.kodeitem,
                ItemSellingPrice.satuan == Item.satuan
            )
        ).filter(
            ItemStock.stok <= Item.stokmin
        ).group_by(
            Item.kodeitem,
            Item.namaitem,
            ItemSellingPrice.hargajual,
            ItemStock.stok,
            Item.stokmin,
            Item.keterangan
        ).order_by(Item.kodeitem.asc())

    # First try to get data from current database
    query = build_query(db)
    results = query.all()
    
    # If no results found, try old database
    if not results:
        from database.database_connection import get_db
        old_db_gen = get_db("old")
        old_db = next(old_db_gen)
        try:
            old_query = build_query(old_db)
            results = old_query.all()
        finally:
            old_db.close()
    
    items = [
        {
            'code': result.code,
            'name': format_stock_and_name(result.stok, result.name, result.stokmin),
            'price': result.price,
            'details': result.detail if result.detail else '',
            'total_sold': result.total_sold
        }
        for result in results
    ]

    return templates.TemplateResponse(
        "monthly_low_stock_items.html",
        {
            "request": request,
            "items": items,
            "current_month": month,
            "current_year": year,
            "prev_month": prev_month,
            "prev_year": prev_year,
            "next_month": next_month,
            "next_year": next_year,
            "month_name": calendar.month_name[month]
        }
    )

def format_number(value):
    """Format number to remove decimal if it's a whole number"""
    if value is None:
        return "0"
    return f"{value:.2f}".rstrip('0').rstrip('.')

def format_stock_and_name(stock, name, stokmin):
    """Format stock, name, and minimum stock together"""
    formatted_stock = format_number(stock)
    formatted_stokmin = format_number(stokmin if stokmin is not None else 0)
    return f"{formatted_stock} - {name} - {formatted_stokmin}"