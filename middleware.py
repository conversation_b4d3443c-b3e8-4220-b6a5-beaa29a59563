from fastapi import Request, HTTPException, status
from fastapi.responses import RedirectResponse
from jose import JWTError, jwt
import re

from routers.auth_router import SECRET_KEY, ALGORITHM

# Paths that don't require authentication
PUBLIC_PATHS = [
    r"^/login$",
    r"^/token$",
    r"^/static/.*$",
    r"^/wol-toko-page$",
]

async def auth_middleware(request: Request, call_next):
    # Check if the path is public
    path = request.url.path
    if any(re.match(pattern, path) for pattern in PUBLIC_PATHS):
        return await call_next(request)
    
    # Check for token in cookies
    token = request.cookies.get("access_token")
    if not token or not token.startswith("Bearer "):
        return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    
    # Validate token
    try:
        token_value = token.split("Bearer ")[1]
        payload = jwt.decode(token_value, SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        if username is None:
            return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    except JWTError:
        return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    
    # Token is valid, proceed with the request
    return await call_next(request)