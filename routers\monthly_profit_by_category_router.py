from fastapi import APIRouter, Depends, Request, Query
from sqlalchemy import func, text, and_, case
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import calendar
import pytz
from decimal import Decimal
from database.database_connection import get_db
from database.database_model import ItemSalesHeader, ItemSalesDetail, ItemUnitConversion, Item
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates

router = APIRouter()
templates = Jinja2Templates(directory="templates")

# Add currency filter
def currency_filter(value):
    try:
        return f"Rp {int(value):,}"
    except (ValueError, TypeError):
        return "Rp 0"

# Register the currency filter
templates.env.filters["currency"] = currency_filter

def get_current_db():
    return next(get_db("current"))

def get_old_db():
    return next(get_db("old"))

def determine_database_for_year(year: int, db_current: Session, db_old: Session):
    """
    Dynamically determine which database contains data for a specific year
    by checking both databases for transaction records in that year.
    """
    jakarta_tz = pytz.timezone('Asia/Jakarta')
    start_date = datetime(year, 1, 1, tzinfo=jakarta_tz)
    end_date = datetime(year + 1, 1, 1, tzinfo=jakarta_tz)
    
    # Check current database for data in the specified year
    current_db_count = db_current.query(ItemSalesHeader).filter(
        ItemSalesHeader.tanggal >= start_date,
        ItemSalesHeader.tanggal < end_date,
        ItemSalesHeader.tipe == 'KSR'
    ).count()
    
    # Check old database for data in the specified year
    old_db_count = db_old.query(ItemSalesHeader).filter(
        ItemSalesHeader.tanggal >= start_date,
        ItemSalesHeader.tanggal < end_date,
        ItemSalesHeader.tipe == 'KSR'
    ).count()
    
    # Return the database with more records, or current DB if both have data
    if old_db_count > current_db_count:
        return db_old
    else:
        return db_current

def get_category_group(jenis: str) -> str:
    """
    Group categories based on user requirements
    """
    sensor_group = ['Sensor 5800', 'TS800', 'Sensor']
    diesel_group = ['R175', 'R180', 'S195', 'S1115', 'S1125', 'GX160', 'GX200', 'GX270', 'GX390', 'WP', 'ET950', 'ET1500', 'ST3', 'BG328']
    
    if jenis in sensor_group:
        return 'Sensor Group'
    elif jenis in diesel_group:
        return 'Diesel Group'
    else:
        return jenis

@router.get("/monthly-profit-by-category", response_class=HTMLResponse)
async def get_monthly_profit_by_category(
    request: Request, 
    month: int = Query(default=None, description="Month (1-12)"),
    year: int = Query(default=None, description="Year"),
    db: Session = Depends(get_current_db), 
    db_old: Session = Depends(get_old_db)
):
    jakarta_tz = pytz.timezone('Asia/Jakarta')
    current_date = datetime.now(jakarta_tz)
    
    # Set default month and year if not provided
    if month is None:
        month = current_date.month
    if year is None:
        year = current_date.year
    
    # Determine which database to use for the selected year
    selected_db = determine_database_for_year(year, db, db_old)
    
    # Calculate date range for the selected month
    start_date = datetime(year, month, 1, tzinfo=jakarta_tz)
    if month < 12:
        end_date = datetime(year, month + 1, 1, tzinfo=jakarta_tz)
    else:
        end_date = datetime(year + 1, 1, 1, tzinfo=jakarta_tz)
    
    # Query to get profit by category with grouping
    category_profits = selected_db.query(
        Item.jenis,
        func.sum(ItemSalesDetail.total).label('gross_sales'),
        func.sum(
            ItemSalesDetail.total - (ItemUnitConversion.hargapokok * ItemSalesDetail.jumlah)
        ).label('net_profit')
    ).join(
        ItemSalesDetail,
        Item.kodeitem == ItemSalesDetail.kodeitem
    ).join(
        ItemSalesHeader,
        ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
    ).outerjoin(
        ItemUnitConversion,
        and_(
            ItemSalesDetail.kodeitem == ItemUnitConversion.kodeitem,
            ItemSalesDetail.satuan == ItemUnitConversion.satuan
        )
    ).filter(
        ItemSalesHeader.tanggal >= start_date,
        ItemSalesHeader.tanggal < end_date,
        ItemSalesHeader.tipe == 'KSR'
    ).group_by(
        Item.jenis
    ).all()
    
    # Group the results according to user requirements
    grouped_profits = {}
    for jenis, gross_sales, net_profit in category_profits:
        group_name = get_category_group(jenis)
        
        if group_name not in grouped_profits:
            grouped_profits[group_name] = {
                'gross_sales': 0,
                'net_profit': 0,
                'categories': []
            }
        
        gross_sales_val = float(gross_sales or 0)
        net_profit_val = float(net_profit or 0)
        
        grouped_profits[group_name]['gross_sales'] += gross_sales_val
        grouped_profits[group_name]['net_profit'] += net_profit_val
        grouped_profits[group_name]['categories'].append({
            'name': jenis,
            'gross_sales': gross_sales_val,
            'net_profit': net_profit_val
        })
    
    # Calculate total sales for percentage calculations
    total_sales = sum(group_data['gross_sales'] for group_data in grouped_profits.values())
    
    # Calculate contribution percentage for each group
    for group_name, group_data in grouped_profits.items():
        if total_sales > 0:
            group_data['contribution_percentage'] = (group_data['gross_sales'] / total_sales) * 100
        else:
            group_data['contribution_percentage'] = 0
        
        # Calculate contribution percentage for individual categories within each group
        group_total_sales = group_data['gross_sales']
        for category in group_data['categories']:
            if group_total_sales > 0:
                category['group_contribution_percentage'] = (category['gross_sales'] / group_total_sales) * 100
            else:
                category['group_contribution_percentage'] = 0
            
            if total_sales > 0:
                category['total_contribution_percentage'] = (category['gross_sales'] / total_sales) * 100
            else:
                category['total_contribution_percentage'] = 0
    
    # Combine small groups (<=1%) into "Others" group
    others_group = {
        'gross_sales': 0,
        'net_profit': 0,
        'categories': [],
        'contribution_percentage': 0
    }
    
    filtered_groups = {}
    
    for group_name, group_data in grouped_profits.items():
        if group_data['contribution_percentage'] <= 1.0:
            # Add to others group
            others_group['gross_sales'] += group_data['gross_sales']
            others_group['net_profit'] += group_data['net_profit']
            others_group['categories'].extend(group_data['categories'])
        else:
            # Keep as separate group
            filtered_groups[group_name] = group_data
    
    # Add others group if it has any data
    if others_group['gross_sales'] > 0:
        if total_sales > 0:
            others_group['contribution_percentage'] = (others_group['gross_sales'] / total_sales) * 100
        filtered_groups['Others'] = others_group
    
    # Update grouped_profits to use filtered groups
    grouped_profits = filtered_groups
    
    # Sort by net profit descending
    sorted_groups = sorted(
        grouped_profits.items(), 
        key=lambda x: x[1]['net_profit'], 
        reverse=True
    )
    
    # Generate month and year options for the selector
    months = [{'value': i, 'name': calendar.month_name[i]} for i in range(1, 13)]
    years = list(range(current_date.year - 5, current_date.year + 1))
    
    # Calculate overall profit percentage
    total_gross_sales = sum(group[1]['gross_sales'] for group in sorted_groups)
    total_net_profit = sum(group[1]['net_profit'] for group in sorted_groups)
    overall_profit_percentage = (total_net_profit / total_gross_sales * 100) if total_gross_sales > 0 else 0
    
    return templates.TemplateResponse(
        "monthly_profit_by_category.html",
        {
            "request": request,
            "selected_month": month,
            "selected_year": year,
            "month_name": calendar.month_name[month],
            "grouped_profits": sorted_groups,
            "months": months,
            "years": years,
            "total_gross_sales": total_gross_sales,
            "total_net_profit": total_net_profit,
            "overall_profit_percentage": overall_profit_percentage
        }
    )