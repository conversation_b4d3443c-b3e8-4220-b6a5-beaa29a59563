from fastapi import APIRouter, Depends, Request, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from sqlalchemy import func, or_, desc, case, literal, text, extract
from datetime import datetime, timedelta
import pytz
from database.database_connection import get_db
from database.database_model import (
    Item,
    ItemSalesHeader,
    ItemSalesDetail,
    ItemPurchaseHeader,
    ItemPurchaseDetail,
    ItemStock,
    ItemUnitConversion,
    ItemStockOpname,
)
from collections import defaultdict
import calendar
from typing import Optional, Any
from pydantic import BaseModel
from decimal import Decimal

router = APIRouter(
    prefix="",
    tags=["item_history"]
)

templates = Jinja2Templates(directory="templates")

class StockUpdate(BaseModel):
    kodeitem: str
    new_stock: float
    satuan: str

@router.post("/api/update-stock")
async def update_stock(
    stock_update: StockUpdate,
    db: Session = Depends(get_db)
):
    # Get current stock
    current_stock = db.query(ItemStock.stok)\
        .filter(
            ItemStock.kodeitem == stock_update.kodeitem,
            ItemStock.kantor == 'UTM'
        ).scalar() or 0

    # Get item's base price
    base_price = db.query(ItemUnitConversion.hargapokok)\
        .filter(
            ItemUnitConversion.kodeitem == stock_update.kodeitem,
            ItemUnitConversion.satuan == stock_update.satuan
        ).scalar() or 0

    # Convert values to Decimal for calculation
    base_price = Decimal(str(base_price))
    stock_difference = Decimal(str(stock_update.new_stock)) - Decimal(str(current_stock))
    
    # Get current date
    now = datetime.now()
    # Use correct month number in period ID
    period_id = now.strftime(f"OPNA{now.month}E-%Y%m%d")
    detail_id = f"{now.strftime('%Y%m%d')}-{stock_update.kodeitem}-UTM"

    # Create stock opname record
    new_opname = ItemStockOpname(
        iddetail=detail_id,
        periode=period_id,
        tanggal=now,
        kodeitem=stock_update.kodeitem,
        kodekantor='UTM',
        satuan=stock_update.satuan,
        jmlsebelum=float(current_stock),  # Convert to float for database
        jmlfisik=float(stock_update.new_stock),  # Convert to float for database
        jmlselisih=float(stock_difference),  # Convert to float for database
        kodeacc='5-2200',
        user1='J',
        user2=None,
        dateupd=now,
        harga=base_price,
        total=base_price * stock_difference,
        compname='RYZEN5600G'
    )

    try:
        # Update stock
        db.query(ItemStock).filter(
            ItemStock.kodeitem == stock_update.kodeitem,
            ItemStock.kantor == 'UTM'
        ).update({
            'stok': float(stock_update.new_stock)  # Convert to float for database
        })

        # Add opname record
        db.add(new_opname)
        db.commit()
        return {"success": True}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/item-history/{kodeitem}", response_class=HTMLResponse)
async def get_item_history(
    request: Request,
    kodeitem: str,
    current_db: Session = Depends(lambda: next(get_db("current"))),
    old_db: Session = Depends(lambda: next(get_db("old")))
):
    # Add current date in Jakarta timezone
    jakarta_tz = pytz.timezone('Asia/Jakarta')
    now = datetime.now(jakarta_tz)
    
    # Convert kodeitem to lowercase for comparison
    kodeitem = kodeitem.lower()
    
    # Get item details using case-insensitive comparison from current DB
    item = current_db.query(Item).filter(func.lower(Item.kodeitem) == kodeitem).first()
    
    current_stock = current_db.query(ItemStock.stok)\
        .filter(
            func.lower(ItemStock.kodeitem) == kodeitem,
            ItemStock.kantor == 'UTM'
        ).scalar() or 0

    # Get sales and purchase history from current DB
    current_sales_history = current_db.query(
        ItemSalesHeader.tanggal.label('tanggal'),
        ItemSalesHeader.notransaksi.label('notransaksi'),
        ItemSalesHeader.tipe.label('tipe'),
        ItemSalesDetail.jumlah.label('jumlah'),
        ItemSalesDetail.satuan.label('satuan'),
        ItemSalesDetail.harga.label('harga'),
        ItemSalesDetail.total.label('total'),
        ItemSalesHeader.kodesupel.label('kodesupel'),
        ItemSalesHeader.keterangan.label('keterangan'),
        literal('current').label('db_source')  # Add source identifier
    ).join(
        ItemSalesDetail,
        ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
    ).filter(
        func.lower(ItemSalesDetail.kodeitem) == kodeitem,
        ItemSalesHeader.tipe.in_(['KSR', 'RJ', 'IK', 'JL'])
    )
    
    # Get sales history from old DB
    old_sales_history = old_db.query(
        ItemSalesHeader.tanggal.label('tanggal'),
        ItemSalesHeader.notransaksi.label('notransaksi'),
        ItemSalesHeader.tipe.label('tipe'),
        ItemSalesDetail.jumlah.label('jumlah'),
        ItemSalesDetail.satuan.label('satuan'),
        ItemSalesDetail.harga.label('harga'),
        ItemSalesDetail.total.label('total'),
        ItemSalesHeader.kodesupel.label('kodesupel'),
        ItemSalesHeader.keterangan.label('keterangan'),
        literal('old').label('db_source')  # Add source identifier
    ).join(
        ItemSalesDetail,
        ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
    ).filter(
        func.lower(ItemSalesDetail.kodeitem) == kodeitem,
        ItemSalesHeader.tipe.in_(['KSR', 'RJ', 'IK', 'JL'])
    )

    # Get purchase history from current DB
    current_purchase_history = current_db.query(
        ItemPurchaseHeader.tanggal.label('tanggal'),
        ItemPurchaseHeader.notransaksi.label('notransaksi'),
        ItemPurchaseHeader.tipe.label('tipe'),
        ItemPurchaseDetail.jumlah.label('jumlah'),
        ItemPurchaseDetail.satuan.label('satuan'),
        ItemPurchaseDetail.harga.label('harga'),
        ItemPurchaseDetail.total.label('total'),
        ItemPurchaseHeader.kodesupel.label('kodesupel'),
        ItemPurchaseHeader.keterangan.label('keterangan'),
        literal('current').label('db_source')  # Add source identifier
    ).join(
        ItemPurchaseDetail,
        ItemPurchaseHeader.notransaksi == ItemPurchaseDetail.notransaksi
    ).filter(
        func.lower(ItemPurchaseDetail.kodeitem) == kodeitem,
        ItemPurchaseHeader.tipe.in_(['BL', 'IM'])
    )
    
    # Get purchase history from old DB
    old_purchase_history = old_db.query(
        ItemPurchaseHeader.tanggal.label('tanggal'),
        ItemPurchaseHeader.notransaksi.label('notransaksi'),
        ItemPurchaseHeader.tipe.label('tipe'),
        ItemPurchaseDetail.jumlah.label('jumlah'),
        ItemPurchaseDetail.satuan.label('satuan'),
        ItemPurchaseDetail.harga.label('harga'),
        ItemPurchaseDetail.total.label('total'),
        ItemPurchaseHeader.kodesupel.label('kodesupel'),
        ItemPurchaseHeader.keterangan.label('keterangan'),
        literal('old').label('db_source')  # Add source identifier
    ).join(
        ItemPurchaseDetail,
        ItemPurchaseHeader.notransaksi == ItemPurchaseDetail.notransaksi
    ).filter(
        func.lower(ItemPurchaseDetail.kodeitem) == kodeitem,
        ItemPurchaseHeader.tipe.in_(['BL', 'IM'])
    )

    # Get stock opname history from current DB
    current_opname_history = current_db.query(
        ItemStockOpname.tanggal,
        ItemStockOpname.periode,
        literal('OP').label('tipe'),
        ItemStockOpname.jmlselisih.label('jumlah'),
        ItemStockOpname.satuan,
        ItemStockOpname.harga,
        ItemStockOpname.total,
        literal(None).label('kodesupel'),
        literal('').label('keterangan'),
        literal('current').label('db_source')  # Add source identifier
    ).filter(
        func.lower(ItemStockOpname.kodeitem) == kodeitem.lower()
    )
    
    # Get stock opname history from old DB
    old_opname_history = old_db.query(
        ItemStockOpname.tanggal,
        ItemStockOpname.periode,
        literal('OP').label('tipe'),
        ItemStockOpname.jmlselisih.label('jumlah'),
        ItemStockOpname.satuan,
        ItemStockOpname.harga,
        ItemStockOpname.total,
        literal(None).label('kodesupel'),
        literal('').label('keterangan'),
        literal('old').label('db_source')  # Add source identifier
    ).filter(
        func.lower(ItemStockOpname.kodeitem) == kodeitem.lower()
    )
    # Calculate monthly sales totals
    current_date = datetime.now()
    two_years_ago = current_date - timedelta(days=730)  # Get data from the last 2 years
    
    # Format the monthly sales data
    monthly_sales = []
    
    # Get current year and month
    current_year = current_date.year
    current_month = current_date.month
    
    # Get unit conversion information for the item
    unit_conversions = {}
    current_conversions = current_db.query(
        ItemUnitConversion.satuan,
        ItemUnitConversion.jumlahkonv,
        ItemUnitConversion.tipe
    ).filter(
        func.lower(ItemUnitConversion.kodeitem) == kodeitem
    ).all()
    
    for satuan, jumlahkonv, tipe in current_conversions:
        unit_conversions[satuan] = {'jumlahkonv': jumlahkonv, 'tipe': tipe}
    
    # Generate 12 months starting from current month last year to current month this year
    # For example: if current is Sep 2025, show Oct 2024, Nov 2024, ..., Sep 2025
    for i in range(12):
        # Calculate the month and year for each position
        target_month = current_month - 11 + i  # Start from 11 months ago
        target_year = current_year
        
        # Handle year rollover
        if target_month <= 0:
            target_month += 12
            target_year -= 1
        
        # Create start and end dates for the month
        start_date = datetime(target_year, target_month, 1)
        if target_month == 12:
            end_date = datetime(target_year + 1, 1, 1)
        else:
            end_date = datetime(target_year, target_month + 1, 1)
        
        # Get sales from current DB with unit conversion
        current_sales_with_units = current_db.query(
            ItemSalesDetail.jumlah,
            ItemSalesDetail.satuan
        ).join(
            ItemSalesHeader,
            ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
        ).filter(
            func.lower(ItemSalesDetail.kodeitem) == kodeitem,
            ItemSalesHeader.tipe == 'KSR',
            ItemSalesHeader.tanggal >= start_date,
            ItemSalesHeader.tanggal < end_date
        ).all()
        
        # Calculate total sales with unit conversion for current DB
        current_sales = 0
        for sale in current_sales_with_units:
            if sale.satuan in unit_conversions:
                if unit_conversions[sale.satuan]['tipe'] == 'K':  # Conversion unit
                    current_sales += sale.jumlah * unit_conversions[sale.satuan]['jumlahkonv']
                else:  # Base unit
                    current_sales += sale.jumlah
            else:
                # If no conversion found, use as is
                current_sales += sale.jumlah
        
        # Only use old DB data if current DB has no data for this month
        # and only for months before 2024 (or if target_year < 2024)
        total_sales = current_sales
        
        # If no data in current DB and it's before 2024, check old DB
        if current_sales == 0 and target_year < 2024:
            # Get sales from old DB with unit conversion
            old_sales_with_units = old_db.query(
                ItemSalesDetail.jumlah,
                ItemSalesDetail.satuan
            ).join(
                ItemSalesHeader,
                ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
            ).filter(
                func.lower(ItemSalesDetail.kodeitem) == kodeitem,
                ItemSalesHeader.tipe == 'KSR',
                ItemSalesHeader.tanggal >= start_date,
                ItemSalesHeader.tanggal < end_date
            ).all()
            
            # Calculate total sales with unit conversion for old DB
            old_sales = 0
            for sale in old_sales_with_units:
                if sale.satuan in unit_conversions:
                    if unit_conversions[sale.satuan]['tipe'] == 'K':  # Conversion unit
                        old_sales += sale.jumlah * unit_conversions[sale.satuan]['jumlahkonv']
                    else:  # Base unit
                        old_sales += sale.jumlah
                else:
                    # If no conversion found, use as is
                    old_sales += sale.jumlah
            
            total_sales = old_sales
        
        month_name = calendar.month_name[target_month]
        monthly_sales.append({
            'year': target_year,
            'month': target_month,
            'month_name': month_name,
            'units': int(total_sales)
        })
    
    # Debug: Print monthly sales data
    print(f"\nMonthly Sales Summary (12 months chronological):")
    for entry in monthly_sales:
        print(f"{entry['month_name']} {entry['year']}: {entry['units']} units")
    # Use history from both current and old database
    # Prioritize current database data and only add old database records that don't exist in current database
    combined_history = []
    
    # Add current database records
    current_sales = current_sales_history.all()
    current_purchases = current_purchase_history.all()
    current_opnames = current_opname_history.all()
    
    # Add old database records
    old_sales = old_sales_history.all()
    old_purchases = old_purchase_history.all()
    old_opnames = old_opname_history.all()
    
    # Create sets of transaction numbers from current database to avoid duplicates
    current_transaction_numbers = set()
    
    # Collect transaction numbers from current database
    for record in current_sales + current_purchases:
        if hasattr(record, 'notransaksi') and record.notransaksi:
            current_transaction_numbers.add(record.notransaksi)
    
    # Collect opname periods from current database (opnames use periode instead of notransaksi)
    current_opname_periods = set()
    for record in current_opnames:
        if hasattr(record, 'periode') and record.periode:
            current_opname_periods.add(record.periode)
    
    # Start with all current database records
    combined_history = current_sales + current_purchases + current_opnames
    
    # Add old database records only if they don't exist in current database
    for record in old_sales + old_purchases:
        if hasattr(record, 'notransaksi') and record.notransaksi:
            if record.notransaksi not in current_transaction_numbers:
                combined_history.append(record)
    
    # Add old opname records only if they don't exist in current database
    for record in old_opnames:
        if hasattr(record, 'periode') and record.periode:
            if record.periode not in current_opname_periods:
                combined_history.append(record)
    
    # Debug: Print record counts
    print(f"\nCurrent sales: {len(current_sales)}")
    print(f"Current purchases: {len(current_purchases)}")
    print(f"Current opnames: {len(current_opnames)}")
    print(f"Old sales: {len(old_sales)}")
    print(f"Old purchases: {len(old_purchases)}")
    print(f"Old opnames: {len(old_opnames)}")
    print(f"Combined total: {len(combined_history)}")
    
    # Debug: Print some records from each source
    print("\n==== SAMPLE RECORDS ====")
    
    # Print 2024 records from old database
    print("\n2024 Records from Old Database:")
    for record in old_sales:
        if record.tanggal and record.tanggal.year == 2024:
            print(f"Date: {record.tanggal}, Transaction: {record.notransaksi}, Type: {record.tipe}, Source: {record.db_source}")
    
    # Print first few records from combined history
    print("\nFirst 10 Combined Records:")
    for i, record in enumerate(combined_history[:10]):
        transaction = record.notransaksi if hasattr(record, 'notransaksi') else 'N/A'
        if not hasattr(record, 'notransaksi') and hasattr(record, 'tipe') and record.tipe == 'OP':
            transaction = 'N/A (Opname)'
        print(f"{i} - Date: {record.tanggal}, Transaction: {transaction}, Type: {record.tipe}, Source: {record.db_source}")
    print("...")

    # Get unit conversions for the item from current DB
    unit_conversions = current_db.query(ItemUnitConversion)\
        .filter(func.lower(ItemUnitConversion.kodeitem) == kodeitem)\
        .all()

    # Remove suppliers query - we'll use kodesupel directly

    # Sort combined_history by date in ascending order for stock calculation
    combined_history = sorted(combined_history, key=lambda x: x.tanggal if hasattr(x, 'tanggal') else datetime.min)
    
    # Calculate running stock balance starting from current stock
    history_data = []
    running_stock = current_stock
    
    # Process history records in reverse (newest to oldest)
    for record in reversed(combined_history):
        # Check if record has all required attributes
        if not all(hasattr(record, attr) for attr in ['tanggal', 'tipe', 'jumlah', 'satuan', 'harga', 'total', 'db_source']):
            print(f"Skipping record with missing attributes: {record}")
            continue
            
        # Get notransaksi safely
        if hasattr(record, 'notransaksi'):
            notransaksi = record.notransaksi
        else:
            # Special handling for opname records
            if hasattr(record, 'tipe') and record.tipe == 'OP':
                # Use the periode field for opname records instead of 'N/A (Opname)'
                notransaksi = f"{record.periode} (Opname)" if hasattr(record, 'periode') else 'N/A (Opname)'
            else:
                notransaksi = 'N/A'
        
        # First add the current record with current running_stock
        history_data.append({
            'tanggal': record.tanggal,
            'notransaksi': notransaksi,
            'tipe': record.tipe,
            'jumlah': record.jumlah,
            'satuan': record.satuan,
            'harga': record.harga,
            'total': record.total,
            'running_stock': running_stock,
            'supplier_name': record.kodesupel if hasattr(record, 'kodesupel') else '',
            'keterangan': record.keterangan if hasattr(record, 'keterangan') else '',
            'db_source': record.db_source  # Add database source information
        })
        
        # Then calculate the next (previous) running_stock
        if record.tipe in ['KSR', 'IK', 'JL']:  # Sales/Outgoing
            stock_change = -record.jumlah  # Subtract sales quantity (will be added to previous balance)
        elif record.tipe in ['RJ', 'BL', 'IM']:  # Returns/Purchase/Incoming
            stock_change = record.jumlah  # Add returns/purchase quantity (will be subtracted from previous balance)
        elif record.tipe == 'OP':  # Stock Opname
            stock_change = record.jumlah  # Add/subtract opname difference
            
        # Convert to base unit if necessary
        for conv in unit_conversions:
            if conv.satuan == record.satuan and conv.tipe == 'K':
                stock_change *= conv.jumlahkonv
                break
                
        running_stock -= stock_change  # Calculate previous balance

    return templates.TemplateResponse(
        "item_history.html",
        {
            "request": request,
            "item": item,
            "current_stock": current_stock,
            "history": history_data,
            "monthly_sales": monthly_sales,
            "now": now,  # Pass the current date to the template
        }
    )

@router.get("/api/transaction-details/{notransaksi:path}")
async def get_transaction_details(
    notransaksi: str, 
    current_db: Session = Depends(lambda: next(get_db("current"))),
    old_db: Session = Depends(lambda: next(get_db("old")))
):
    try:
        # Try to query the current database first
        sales_details = current_db.query(
            ItemSalesDetail.kodeitem,
            Item.namaitem,
            ItemSalesDetail.jumlah,
            ItemSalesDetail.satuan,
            ItemSalesDetail.harga,
            ItemSalesDetail.total,
            ItemSalesDetail.potongan,
            ItemSalesHeader.tanggal,
            ItemSalesHeader.tipe,
            ItemSalesHeader.potfaktur,
            ItemSalesHeader.potnomfaktur,
            ItemSalesHeader.totalakhir,
            literal('current').label('db_source')
        ).join(
            Item,
            Item.kodeitem == ItemSalesDetail.kodeitem
        ).join(
            ItemSalesHeader,
            ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
        ).filter(
            ItemSalesDetail.notransaksi == notransaksi
        ).all()
        
        # If not found in current DB, try the old DB
        if not sales_details:
            sales_details = old_db.query(
                ItemSalesDetail.kodeitem,
                Item.namaitem,
                ItemSalesDetail.jumlah,
                ItemSalesDetail.satuan,
                ItemSalesDetail.harga,
                ItemSalesDetail.total,
                ItemSalesDetail.potongan,
                ItemSalesHeader.tanggal,
                ItemSalesHeader.tipe,
                ItemSalesHeader.potfaktur,
                ItemSalesHeader.potnomfaktur,
                ItemSalesHeader.totalakhir,
                literal('old').label('db_source')
            ).join(
                Item,
                Item.kodeitem == ItemSalesDetail.kodeitem
            ).join(
                ItemSalesHeader,
                ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
            ).filter(
                ItemSalesDetail.notransaksi == notransaksi
            ).all()

        # Query for purchase transaction if no sales found
        purchase_details = None
        if not sales_details:
            # Try current DB first
            purchase_details = current_db.query(
                ItemPurchaseDetail.kodeitem,
                Item.namaitem,
                ItemPurchaseDetail.jumlah,
                ItemPurchaseDetail.satuan,
                ItemPurchaseDetail.harga,
                ItemPurchaseDetail.total,
                ItemPurchaseDetail.potongan,
                ItemPurchaseHeader.tanggal,
                ItemPurchaseHeader.tipe,
                ItemPurchaseHeader.potfaktur,
                ItemPurchaseHeader.potnomfaktur,
                ItemPurchaseHeader.totalakhir,
                literal('current').label('db_source')
            ).join(
                Item,
                Item.kodeitem == ItemPurchaseDetail.kodeitem
            ).join(
                ItemPurchaseHeader,
                ItemPurchaseHeader.notransaksi == ItemPurchaseDetail.notransaksi
            ).filter(
                ItemPurchaseDetail.notransaksi == notransaksi
            ).all()
            
            # If not found in current DB, try old DB
            if not purchase_details:
                purchase_details = old_db.query(
                    ItemPurchaseDetail.kodeitem,
                    Item.namaitem,
                    ItemPurchaseDetail.jumlah,
                    ItemPurchaseDetail.satuan,
                    ItemPurchaseDetail.harga,
                    ItemPurchaseDetail.total,
                    ItemPurchaseDetail.potongan,
                    ItemPurchaseHeader.tanggal,
                    ItemPurchaseHeader.tipe,
                    ItemPurchaseHeader.potfaktur,
                    ItemPurchaseHeader.potnomfaktur,
                    ItemPurchaseHeader.totalakhir,
                    literal('old').label('db_source')
                ).join(
                    Item,
                    Item.kodeitem == ItemPurchaseDetail.kodeitem
                ).join(
                    ItemPurchaseHeader,
                    ItemPurchaseHeader.notransaksi == ItemPurchaseDetail.notransaksi
                ).filter(
                    ItemPurchaseDetail.notransaksi == notransaksi
                ).all()

        # Combine results
        details = sales_details or purchase_details
        if not details:
            raise HTTPException(status_code=404, detail=f"Transaction {notransaksi} not found")

        # Get the first record for header info
        first_record = details[0]
        
        return {
            "header": {
                "notransaksi": notransaksi,
                "tanggal": first_record.tanggal,
                "tipe": first_record.tipe,
                "potfaktur": float(first_record.potfaktur or 0),
                "potnomfaktur": float(first_record.potnomfaktur or 0),
                "totalakhir": float(first_record.totalakhir or 0),
                "db_source": first_record.db_source  # Add database source
            },
            "details": [
                {
                    "kodeitem": detail.kodeitem,
                    "namaitem": detail.namaitem,
                    "jumlah": float(detail.jumlah),
                    "satuan": detail.satuan,
                    "harga": float(detail.harga),
                    "total": float(detail.total),
                    "potongan": float(detail.potongan or 0),
                    "db_source": detail.db_source  # Add database source
                }
                for detail in details
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
