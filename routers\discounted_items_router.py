from fastapi import APIRouter, Depends, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from datetime import datetime
from database.database_connection import get_db
from database.database_model import ItemSalesHeader, ItemSalesDetail, Item
import pytz
import calendar
from decimal import Decimal

router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/discounted-items", response_class=HTMLResponse)
async def get_discounted_items(
    request: Request,
    month: int = None,
    year: int = None,
    db: Session = Depends(get_db)
):
    # Get current date in Jakarta timezone
    jakarta_tz = pytz.timezone('Asia/Jakarta')
    current_date = datetime.now(jakarta_tz)
    
    # Use provided month/year or default to current
    month = month or current_date.month
    year = year or current_date.year
    
    # Calculate previous and next month/year
    if month == 1:
        prev_month, prev_year = 12, year - 1
    else:
        prev_month, prev_year = month - 1, year
        
    if month == 12:
        next_month, next_year = 1, year + 1
    else:
        next_month, next_year = month + 1, year
    
    # Function to build the query for a given database session
    def build_discounted_items_query(db_session):
        return db_session.query(
            ItemSalesHeader.notransaksi,
            ItemSalesHeader.tanggal,
            Item.namaitem,
            ItemSalesDetail.jumlah,
            ItemSalesDetail.harga,
            ItemSalesDetail.total,
            ItemSalesDetail.potongan,
            ItemSalesHeader.potfaktur,
            ItemSalesHeader.potnomfaktur,
            ItemSalesHeader.totalakhir
        ).join(
            ItemSalesDetail,
            ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
        ).join(
            Item,
            ItemSalesDetail.kodeitem == Item.kodeitem
        ).filter(
            and_(
                func.extract('month', ItemSalesHeader.tanggal) == month,
                func.extract('year', ItemSalesHeader.tanggal) == year,
                ItemSalesHeader.tipe == 'KSR',
                or_(
                    ItemSalesDetail.potongan > 0,
                    ItemSalesHeader.potfaktur > 0
                )
            )
        ).order_by(
            ItemSalesHeader.tanggal.desc()
        )

    # First try to get data from current database
    discounted_items = build_discounted_items_query(db).all()
    
    # If no results found, try old database
    if not discounted_items:
        from database.database_connection import get_db
        old_db_gen = get_db("old")
        old_db = next(old_db_gen)
        try:
            discounted_items = build_discounted_items_query(old_db).all()
        finally:
            old_db.close()

    # Process the results
    items_data = []
    for item in discounted_items:
        # Convert numeric values to Decimal
        jumlah = Decimal(str(item.jumlah))
        harga = Decimal(str(item.harga))
        total = Decimal(str(item.total))
        potongan = Decimal(str(item.potongan if item.potongan is not None else 0))
        potfaktur = Decimal(str(item.potfaktur if item.potfaktur is not None else 0))

        # Calculate discount amount in Rupiah
        if potongan > 0:  # Item discount
            disc_amount = (harga * jumlah) * (potongan / Decimal('100'))
        else:  # Receipt discount
            # Calculate this item's portion of the total receipt discount
            if potfaktur > 0:
                total_before_disc = total / (Decimal('1') - (potfaktur / Decimal('100')))
                disc_amount = total_before_disc * (potfaktur / Decimal('100'))
            else:
                disc_amount = Decimal('0')

        items_data.append({
            'notransaksi': item.notransaksi,
            'tanggal': item.tanggal,
            'namaitem': item.namaitem,
            'jumlah': float(jumlah),  # Convert back to float for template formatting
            'harga': float(harga),
            'total': float(total),
            'disc_percent': float(potongan or potfaktur or 0),
            'disc_amount': float(disc_amount)
        })

    return templates.TemplateResponse(
        "discounted_items.html",
        {
            "request": request,
            "items": items_data,
            "current_month": month,
            "current_year": year,
            "prev_month": prev_month,
            "prev_year": prev_year,
            "next_month": next_month,
            "next_year": next_year,
            "month_name": calendar.month_name[month]
        }
    )
