<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat History - Toko LARIS LAX</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: "class",
            theme: {
                extend: {
                    backgroundColor: {
                        "gray-750": "#2d3748",
                        "gray-850": "#1a1a1a",
                        "gray-880": "#242424",
                        "gray-900": "#1e293b",
                    },
                },
            },
        };
    </script>
    
    <!-- Add Toastify for notifications -->
    <link
        rel="stylesheet"
        type="text/css"
        href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css"
    />
    <script
        src="https://cdn.jsdelivr.net/npm/toastify-js"
    ></script>
</head>
<body class="bg-black text-gray-100">
    <div class="min-h-screen">
        <!-- Navbar -->
        <nav class="bg-gray-800 shadow-lg">
            <div class="max-w-[95%] mx-auto px-4">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <a href="/" class="text-3xl font-bold text-white hover:text-gray-300 transition-colors duration-150">
                            Toko LARIS LAX
                        </a>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button id="updateTelegramBtn" class="bg-green-600 rounded-lg px-4 py-2 hover:bg-green-700 transition-colors duration-150 cursor-pointer">
                            <span class="text-white">Update Telegram</span>
                        </button>
                        <button onclick="window.close()" class="bg-red-600 rounded-lg px-4 py-2 hover:bg-red-700 transition-colors duration-150 cursor-pointer">
                            <span class="text-white">Close</span>
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="max-w-[95%] mx-auto px-4 py-6">
            <div class="sticky top-0 z-10 bg-gray-900 pb-4 mb-6">
                <div class="flex justify-between items-center mb-4 pt-6">
                    <div>
                        <h1 class="text-3xl font-bold text-white mb-2">Chat History</h1>
                        <p class="text-gray-400">Last 3 months data - Total entries: {{ total_entries }}</p>
                    </div>
                    <div class="flex gap-3">
                        <button id="importFromClipboard" class="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                            Import from Clipboard
                        </button>
                        <button id="exportToExcel" class="bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                            Export to Excel
                        </button>
                        <button id="saveNonBoughtItems" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                            Save Non-Bought Items
                        </button>
                        <button id="toggleNoBuyItems" class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                            Show 'No BUY' Only
                        </button>
                        <button id="toggleBoughtItems" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                            Hide Bought Items
                        </button>
                    </div>
                </div>
                
                <!-- Search Section -->
                <div class="mb-4">
                    <div class="flex items-center gap-4">
                        <div class="flex-1 max-w-md">
                            <div class="relative">
                                <input 
                                    type="text" 
                                    id="searchInput" 
                                    placeholder="Search by item name or jenis..." 
                                    class="w-full px-4 py-2 pl-10 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                >
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <button id="clearSearch" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                            Clear
                        </button>
                        <div id="searchResults" class="text-sm text-gray-400">
                            <!-- Search results count will appear here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat History Table -->
            <div class="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-700">
                        <thead class="bg-gray-700">
                            <tr>
                                <th class="px-4 py-2 text-left text-base font-medium text-gray-300 uppercase tracking-wider">
                                    <input type="checkbox" id="selectAll" class="mr-2 rounded">
                                    Select
                                </th>
                                <th class="px-4 py-2 text-left text-base font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600 transition-colors" onclick="sortTable(1)">
                                    Date
                                    <span class="sort-indicator ml-1">↕</span>
                                </th>
                                <th class="px-4 py-2 text-left text-base font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600 transition-colors" onclick="sortTable(2)">
                                    Item Name
                                    <span class="sort-indicator ml-1">↕</span>
                                </th>
                                <th class="px-4 py-2 text-left text-base font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600 transition-colors" onclick="sortTable(3)">
                                    Jenis
                                    <span class="sort-indicator ml-1">↕</span>
                                </th>
                                <th class="px-4 py-2 text-left text-base font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600 transition-colors" onclick="sortTable(4)">
                                    Stock
                                    <span class="sort-indicator ml-1">↕</span>
                                </th>
                                <th class="px-4 py-2 text-left text-base font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600 transition-colors" onclick="sortTable(5)">
                                    Buying Price
                                    <span class="sort-indicator ml-1">↕</span>
                                </th>
                                <th class="px-4 py-2 text-left text-base font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600 transition-colors" onclick="sortTable(6)">
                                    Sender
                                    <span class="sort-indicator ml-1">↕</span>
                                </th>
                                <th class="px-4 py-2 text-left text-base font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600 transition-colors" onclick="sortTable(7)">
                                    Type
                                    <span class="sort-indicator ml-1">↕</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-gray-800 divide-y divide-gray-700">
                            {% if chat_data %}
                                {% for entry in chat_data %}
                                    {% if entry.get('error') %}
                                        <tr>
                                            <td colspan="8" class="px-4 py-2 text-center text-red-400">
                                                {{ entry.error }}
                                            </td>
                                        </tr>
                                    {% else %}
                                        <tr class="item-row hover:bg-gray-750 transition-colors duration-150{% if entry.get('strikethrough') %} bought-item{% endif %}{% if 'No BUY' in (entry.get('message', '') + entry.get('text', '') + entry.get('content', '')) %} no-buy-item{% endif %}" data-item-id="{{ loop.index }}">
                                            <td class="px-4 py-2 whitespace-nowrap text-base text-gray-300">
                                                <input type="checkbox" class="item-checkbox mr-2 rounded" data-item-id="{{ loop.index }}">
                                            </td>
                                            <td class="px-4 py-2 whitespace-nowrap text-base date-cell" data-date="{% if entry.get('date') %}{{ entry.date }}{% endif %}">
                                {% if entry.get('date') %}
                                    {{ entry.date[:19].replace('T', ' ') }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                                            <td class="px-4 py-2 text-base text-gray-100 max-w-md">
                                {% if entry.get('kodeitem') %}
                                     <div class="break-words{% if entry.get('strikethrough') %} line-through{% endif %} flex items-center justify-between group">
                                         <div class="flex-1">
                                             <a href="/item-history/{{ entry.kodeitem }}" target="_blank" class="{% if entry.get('strikethrough') %}text-red-500{% else %}text-blue-400 hover:text-blue-300{% endif %} transition-colors duration-150 inline">
                                                {% if entry.get('message') %}
                                                    {{ entry.message[:200] }}{% if entry.message|length > 200 %}...{% endif %}
                                                {% elif entry.get('text') %}
                                                    {{ entry.text[:200] }}{% if entry.text|length > 200 %}...{% endif %}
                                                {% elif entry.get('content') %}
                                                    {{ entry.content[:200] }}{% if entry.content|length > 200 %}...{% endif %}
                                                {% else %}
                                                    No message content
                                                {% endif %}
                                            </a>
                                         </div>
                                        <button onclick="copyToClipboard('{% if entry.get('message') %}{{ entry.message|replace("'", "\\'")|replace('"', '\\"') }}{% elif entry.get('text') %}{{ entry.text|replace("'", "\\'")|replace('"', '\\"') }}{% elif entry.get('content') %}{{ entry.content|replace("'", "\\'")|replace('"', '\\"') }}{% endif %}')" class="ml-2 p-1 text-gray-400 hover:text-gray-200 transition-opacity duration-200 flex-shrink-0" title="Copy item name">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                {% else %}
                                     <div class="break-words{% if entry.get('strikethrough') %} line-through text-red-500{% endif %} flex items-center justify-between group">
                                        <span class="flex-1">
                                            {% if entry.get('message') %}
                                                {{ entry.message[:200] }}{% if entry.message|length > 200 %}...{% endif %}
                                            {% elif entry.get('text') %}
                                                {{ entry.text[:200] }}{% if entry.text|length > 200 %}...{% endif %}
                                            {% elif entry.get('content') %}
                                                {{ entry.content[:200] }}{% if entry.content|length > 200 %}...{% endif %}
                                            {% else %}
                                                No message content
                                            {% endif %}
                                        </span>
                                        <button onclick="copyToClipboard('{% if entry.get('message') %}{{ entry.message|replace("'", "\\'")|replace('"', '\\"') }}{% elif entry.get('text') %}{{ entry.text|replace("'", "\\'")|replace('"', '\\"') }}{% elif entry.get('content') %}{{ entry.content|replace("'", "\\'")|replace('"', '\\"') }}{% endif %}')" class="ml-2 p-1 text-gray-400 hover:text-gray-200 transition-opacity duration-200 flex-shrink-0" title="Copy item name">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                {% endif %}
                            </td>
                                            <td class="px-4 py-2 whitespace-nowrap text-base text-gray-300">
                                                {% if entry.get('jenis') %}
                                                    <span class="px-2 py-1 text-xs rounded-full bg-indigo-600 text-white">
                                                        {{ entry.jenis }}
                                                    </span>
                                                {% else %}
                                                    <span class="text-gray-500">-</span>
                                                {% endif %}
                                            </td>
                                            <td class="px-4 py-2 whitespace-nowrap text-base {% if entry.get('stock') is not none and entry.stock < 1 %}text-red-400{% else %}text-gray-300{% endif %}">
                                {% if entry.get('stock') is not none %}
                                    {{ "%.1f"|format(entry.stock) }}
                                {% else %}
                                    <span class="text-gray-500">-</span>
                                {% endif %}
                            </td>
                            <td class="px-4 py-2 whitespace-nowrap text-base text-gray-300">
                                 {% if entry.get('buying_price') is not none %}
                                     {{ "{:,.0f}".format(entry.buying_price) }}
                                 {% else %}
                                     <span class="text-gray-500">-</span>
                                 {% endif %}
                             </td>
                            <td class="px-4 py-2 whitespace-nowrap text-base text-gray-300">
                                                {% if entry.get('sender') %}
                                                    {{ entry.sender }}
                                                {% elif entry.get('from') %}
                                                    {{ entry.from }}
                                                {% elif entry.get('user') %}
                                                    {{ entry.user }}
                                                {% else %}
                                                    Unknown
                                                {% endif %}
                                            </td>
                                            <td class="px-4 py-3 whitespace-nowrap text-base text-gray-300">
                                                {% if entry.get('type') %}
                                                    <span class="px-2 py-1 text-xs rounded-full bg-blue-600 text-white">
                                                        {{ entry.type }}
                                                    </span>
                                                {% elif entry.get('message_type') %}
                                                    <span class="px-2 py-1 text-xs rounded-full bg-green-600 text-white">
                                                        {{ entry.message_type }}
                                                    </span>
                                                {% else %}
                                                    <span class="px-2 py-1 text-xs rounded-full bg-gray-600 text-white">
                                                        Message
                                                    </span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endif %}
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="8" class="px-4 py-2 text-center text-gray-400">
                                        No chat history data found for the last 3 months.
                                    </td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination or Load More (if needed in future) -->
            <div class="mt-6 text-center">
                <p class="text-gray-400 text-sm">
                    Showing {{ total_entries }} entries from the last 3 months
                </p>
            </div>
        </div>
    </div>

    <!-- Chat data for JavaScript -->
    <script>
        // Make chat data available to external JavaScript
        window.chatData = {{ chat_data | tojson | safe }};
    </script>
    </script>
    
    <!-- Clipboard Import Modal -->
    <div id="clipboardModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-gray-800 rounded-lg p-6 w-full max-w-2xl">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-white">Import Items from Clipboard</h2>
                    <button id="closeModal" class="text-gray-400 hover:text-white">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <label for="senderInput" class="block text-sm font-medium text-gray-300 mb-2">Sender:</label>
                        <input type="text" id="senderInput" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter sender name">
                    </div>
                    
                    <div>
                        <label for="clipboardData" class="block text-sm font-medium text-gray-300 mb-2">Paste Excel Data:</label>
                        <textarea id="clipboardData" rows="10" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Paste your Excel data here (one item per line)&#10;Example:&#10;Saringan Udara GX160 KMS&#10;Karburator Kran GX160 KMS&#10;Rumah Start GX160 KMS"></textarea>
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button id="cancelImport" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-150">
                            Cancel
                        </button>
                        <button id="processImport" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-150">
                            Process Import
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Include chat history JavaScript for copy functionality -->
    <script src="{{ url_for('static', path='js/chat-history.js') }}"></script>
</body>
</html>