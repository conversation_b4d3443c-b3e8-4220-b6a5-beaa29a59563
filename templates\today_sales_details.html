<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Today Sales Details - {{ formatted_date }}</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            backgroundColor: {
              "gray-750": "#2d3748",
              "gray-900": "#1e293b",
            },
            colors: {
              magenta: {
                400: "#f0abfc",
              },
            },
          },
        },
      };
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const dateInput = document.getElementById("dateSelector");

        // Get date from URL parameter if it exists
        const urlParams = new URLSearchParams(window.location.search);
        const dateParam = urlParams.get("date");

        if (dateParam) {
          // If date parameter exists in URL, use it
          dateInput.value = dateParam;
        } else {
          // If no date parameter, set to today's date
          const today = new Date();
          dateInput.value = today.toISOString().split("T")[0];
        }
      });

      function changeDate(days) {
        const dateInput = document.getElementById("dateSelector");
        const currentDate = new Date(dateInput.value);
        currentDate.setDate(currentDate.getDate() + days);

        // Format date as YYYY-MM-DD
        const newDate = currentDate.toISOString().split("T")[0];
        dateInput.value = newDate;
        loadDate(newDate);
      }

      function loadDate(date) {
        // Redirect to the same page with date parameter
        window.location.href = `/today-sales-details?date=${date}`;
      }

      function refreshToToday() {
        const today = new Date();
        const todayStr = today.toISOString().split("T")[0];
        loadDate(todayStr);
      }
    </script>
    <script>
      // Add copy on select functionality
      document.addEventListener("mouseup", function () {
        const selectedText = window.getSelection().toString().trim();
        if (selectedText) {
          // Use the copyToClipboard function from today-sales-details.js
          if (typeof copyToClipboard === "function") {
            copyToClipboard(selectedText);
          } else {
            // Fallback if function not available yet
            navigator.clipboard.writeText(selectedText).catch((err) => {
              console.error("Failed to copy text: ", err);
            });
          }
        }
      });
    </script>
    <!-- Add Toastify for notifications -->
    <link
      rel="stylesheet"
      type="text/css"
      href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css"
    />
    <script
      type="text/javascript"
      src="https://cdn.jsdelivr.net/npm/toastify-js"
    ></script>
    <!-- Include today-sales-details.js with defer to ensure it loads properly -->
    <script src="/static/js/today-sales-details.js" defer></script>
    <!-- Include twelve-month-sales utilities -->
    <script src="/static/js/twelve-month-sales-utils.js" defer></script>
  </head>
  <body class="bg-black text-gray-100">
    <div class="min-h-screen" id="mainContent">
      <!-- Navbar - Made sticky -->
      <nav class="bg-gray-800 shadow-lg sticky top-0 z-50">
        <div class="max-w-[95%] mx-auto px-4">
          <div class="flex justify-between h-16">
            <div class="flex items-center space-x-4">
              <h1 class="text-3xl font-bold text-white">Today's Sales</h1>
              <!-- Date Selector -->
              <div
                class="flex items-center space-x-2 bg-gray-700 rounded-lg px-4 py-2"
              >
                <button
                  onclick="changeDate(-1)"
                  class="text-gray-300 hover:text-white transition-colors duration-150"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
                <input
                  type="date"
                  id="dateSelector"
                  class="bg-gray-700 text-white border-none focus:ring-2 focus:ring-blue-500 rounded-md px-2 py-1"
                  onchange="loadDate(this.value)"
                />
                <button
                  onclick="changeDate(1)"
                  class="text-gray-300 hover:text-white transition-colors duration-150"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
              </div>
              <!-- Navigation Buttons -->
              <div class="flex space-x-2">
                <button
                  onclick="openThreeMonthModal()"
                  class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-2"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5z"
                    />
                    <path
                      d="M11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                    />
                  </svg>
                  3-Month
                </button>
                <button
                  onclick="refreshToToday()"
                  class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-2"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Refresh
                </button>
                <button
                  onclick="window.close()"
                  class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-2"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414 1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Close
                </button>
              </div>
            </div>
            <div class="flex items-center space-x-4">
              <div
                class="bg-green-600 rounded-lg px-4 py-2 cursor-pointer hover:bg-green-700 transition-colors duration-150"
                onclick="openTwelveMonthSales()"
                title="Click to view 12-month sales analysis"
              >
                <span class="text-xl text-white">Sales:</span>
                <span class="text-xl font-bold text-white ml-2">
                  {{ "{:,.0f}".format(total_sales) }}
                </span>
              </div>
              <div class="bg-green-600 rounded-lg px-4 py-2">
                <span class="text-xl text-white">Profit:</span>
                <span class="text-xl font-bold text-white ml-2">
                  {{ "{:,.0f}".format(total_profit) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content - Added padding-top to prevent content from hiding under sticky nav -->
      <div class="max-w-[95%] mx-auto px-4 py-4">
        <!-- Table -->
        <div class="overflow-x-auto bg-gray-800 rounded-lg shadow">
          <table class="w-full divide-y divide-gray-700">
            <thead class="bg-gray-700">
              <tr>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
                >
                  No Transaksi
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
                >
                  Kode Item
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
                >
                  Nama Item
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
                >
                  Stok
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-right text-base font-medium text-gray-300 uppercase tracking-wider"
                >
                  Harga
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-right text-base font-medium text-gray-300 uppercase tracking-wider"
                >
                  Total
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-right text-base font-medium text-gray-300 uppercase tracking-wider"
                >
                  Profit
                </th>
                <th
                  scope="col"
                  class="px-4 py-3 text-right text-base font-medium text-gray-300 uppercase tracking-wider"
                >
                  Time
                </th>
              </tr>
            </thead>
            <tbody class="bg-gray-800 divide-y divide-gray-700">
              {% set ns = namespace(previous_notransaksi=None, row_color=True)
              %} {% for sale in sales %} {% if sale.notransaksi !=
              ns.previous_notransaksi %} {% set ns.row_color = not ns.row_color
              %} {% set ns.previous_notransaksi = sale.notransaksi %} {% endif
              %}
              <tr
                class="hover:bg-gray-700 transition duration-150 {{ 'bg-black' if ns.row_color else 'bg-gray-900' }}"
              >
                <td class="px-4 py-3 whitespace-nowrap text-base text-gray-300">
                  <span
                    class="cursor-pointer hover:text-blue-400"
                    onclick="showTransactionDetails('{{ sale.notransaksi }}')"
                  >
                    {{ sale.notransaksi }}
                  </span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-base text-gray-300">
                  <a
                    href="/item-history/{{ sale.kodeitem }}"
                    target="_blank"
                    class="hover:text-blue-400 transition-colors duration-150"
                  >
                    {{ sale.kodeitem }}
                  </a>
                </td>
                <td
                  class="px-4 py-3 whitespace-nowrap text-base {% if sale.stok <= sale.stokmin %}text-red-400{% else %}text-gray-300{% endif %}"
                >
                  <span class="text-yellow-400"
                    >{{ "%.0f"|format(sale.jumlah) }}</span
                  >
                  - {% set parts = sale.namaitem.split(' - ', 2) if ' - ' in
                  sale.namaitem else [sale.namaitem] %} {% if parts|length > 1
                  %}
                  <span
                    class="text-yellow-400 cursor-pointer search-item-name"
                    data-item-name="{{ parts[1] }}"
                    data-item-code="{{ sale.kodeitem }}"
                    >{{ parts[0] }}</span
                  >
                  <span class="text-gray-300"> - </span>
                  <span
                    class="text-gray-300 cursor-pointer search-item-name"
                    data-item-name="{{ parts[1] }}"
                    data-item-code="{{ sale.kodeitem }}"
                    >{{ parts[1] }}</span
                  >
                  {% if parts|length > 2 %}
                  <span class="text-gray-300"> - </span>
                  <span class="text-red-400">{{ parts[2] }}</span>
                  {% endif %}
                  <button
                    class="ml-1 text-gray-400 hover:text-white copy-button"
                    data-copy-text="{{ parts[1] }}"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                      />
                    </svg>
                  </button>
                  <button
                    class="ml-1 text-gray-400 hover:text-green-400"
                    onclick="openTwelveMonthSalesWithSearch('{{ parts[1]|replace('\'', '\\\'') }}')"
                    title="View 12-month sales analysis"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      />
                    </svg>
                  </button>
                  {% else %}
                  <span
                    class="cursor-pointer search-item-name"
                    data-item-name="{{ sale.namaitem }}"
                    data-item-code="{{ sale.kodeitem }}"
                    >{{ sale.namaitem }}</span
                  >
                  <button
                    class="ml-1 text-gray-400 hover:text-white copy-button"
                    data-copy-text="{{ sale.namaitem }}"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                      />
                    </svg>
                  </button>
                  <button
                    class="ml-1 text-gray-400 hover:text-green-400"
                    onclick="openTwelveMonthSalesWithSearch('{{ sale.namaitem|replace('\'', '\\\'') }}')"
                    title="View 12-month sales analysis"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      />
                    </svg>
                  </button>
                  {% endif %}
                </td>
                <td
                  class="px-4 py-3 whitespace-nowrap text-base {% if sale.stok <= sale.stokmin %}text-red-400{% else %}text-gray-300{% endif %}"
                >
                  {{ ("%.2f"|format(sale.stok)).rstrip('0').rstrip('.') }}
                </td>
                <td
                  class="px-4 py-3 whitespace-nowrap text-base text-gray-300 text-right"
                >
                  {{ "{:,.0f}".format(sale.harga) }}
                </td>
                <td
                  class="px-4 py-3 whitespace-nowrap text-base text-right {% if sale.discount > 0 %}bg-yellow-100/30{% endif %}"
                >
                  {% if sale.discount > 0 %}
                  <div class="text-[#f472b6]">
                    {{ "{:,.0f}".format(sale.total) }}
                    <div class="text-sm">
                      -{{ "{:,.0f}".format(sale.discount) }}
                    </div>
                  </div>
                  {% else %}
                  <span class="text-gray-300">
                    {{ "{:,.0f}".format(sale.total) }}
                  </span>
                  {% endif %}
                </td>
                <td
                  class="px-4 py-3 whitespace-nowrap text-base text-right {% if sale.profit > 0 %}text-green-400{% elif sale.profit < 0 %}text-red-400{% else %}text-gray-300{% endif %}"
                >
                  {{ "{:,.0f}".format(sale.profit) }}
                </td>
                <td
                  class="px-4 py-3 whitespace-nowrap text-base text-gray-300 text-right"
                >
                  {{ sale.jam }}
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <!-- Transaction Modal -->
    <div
      id="transactionModal"
      class="fixed inset-0 bg-gray-900 bg-opacity-70 hidden items-center justify-center z-50"
    >
      <div
        class="bg-gray-750 rounded-lg shadow-xl border-2 border-blue-500 w-[95%] max-w-4xl max-h-[90vh] overflow-y-auto p-6"
      >
        <div class="flex justify-between items-center mb-4">
          <div>
            <div class="space-y-2">
              <h3 class="text-xl font-bold">Transaction Details</h3>
              <div class="text-gray-400" id="transactionInfo"></div>
            </div>
          </div>
          <button
            onclick="closeTransactionModal()"
            class="text-gray-400 hover:text-white"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-700">
            <thead class="bg-gray-700">
              <tr>
                <th
                  class="px-4 py-3 text-left text-sm font-medium text-gray-300"
                >
                  Kode Item
                </th>
                <th
                  class="px-4 py-3 text-left text-sm font-medium text-gray-300"
                >
                  Nama Item
                </th>
                <th
                  class="px-4 py-3 text-right text-sm font-medium text-gray-300"
                >
                  Jumlah
                </th>
                <th
                  class="px-4 py-3 text-left text-sm font-medium text-gray-300"
                >
                  Satuan
                </th>
                <th
                  class="px-4 py-3 text-right text-sm font-medium text-gray-300"
                >
                  Harga
                </th>
                <th
                  class="px-4 py-3 text-right text-sm font-medium text-gray-300"
                >
                  Pot (%)
                </th>
                <th
                  class="px-4 py-3 text-right text-sm font-medium text-gray-300"
                >
                  Total
                </th>
              </tr>
            </thead>
            <tbody
              id="transactionDetails"
              class="bg-gray-800 divide-y divide-gray-700"
            ></tbody>
          </table>
        </div>
      </div>
    </div>
    <!-- Remove the external script reference -->
    <!-- <script src="{{ url_for('static', path='js/transaction-modal.js') }}"></script> -->
    <script>
      // Transaction Modal Functions
      function getTransactionType(type) {
        const types = {
          KSR: "Sales",
          BL: "Purchase",
          RJ: "Return",
          IM: "Stock In",
          IK: "Stock Out",
          OP: "Stock Opname",
        };
        return types[type] || type;
      }

      function closeTransactionModal() {
        const modal = document.getElementById("transactionModal");
        modal.classList.remove("flex");
        modal.classList.add("hidden");
      }

      async function showTransactionDetails(notransaksi) {
        try {
          console.log("Showing transaction details for:", notransaksi);
          const encodedNotransaksi = encodeURIComponent(notransaksi);
          const response = await fetch(
            `/api/transaction-details/${encodedNotransaksi}`
          );

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(
              errorData.detail || "Failed to fetch transaction details"
            );
          }

          const data = await response.json();

          // Update transaction info
          const date = new Date(data.header.tanggal).toLocaleDateString(
            "en-US",
            {
              weekday: "short",
              year: "numeric",
              month: "short",
              day: "numeric",
            }
          );

          document.getElementById(
            "transactionInfo"
          ).textContent = `${notransaksi} - ${date} - ${getTransactionType(
            data.header.tipe
          )}`;

          // Calculate total first
          let total = data.details.reduce(
            (sum, detail) => sum + detail.total,
            0
          );

          // Clear previous details
          const tbody = document.getElementById("transactionDetails");
          tbody.innerHTML = "";

          // Remove existing summary section if it exists
          const existingSummary = document.getElementById("summarySection");
          if (existingSummary) {
            existingSummary.remove();
          }

          // Populate details table
          data.details.forEach((detail) => {
            const row = document.createElement("tr");
            row.className = "hover:bg-gray-700";
            row.innerHTML = `
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">${
                detail.kodeitem
              }</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">${
                detail.namaitem
              }</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300 text-right">${
                detail.jumlah
              }</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">${
                detail.satuan
              }</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300 text-right">${detail.harga.toLocaleString()}</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm ${
                detail.potongan > 0 ? "text-red-400" : "text-gray-300"
              } text-right">${
              detail.potongan > 0 ? detail.potongan.toFixed(1) : "-"
            }</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300 text-right">${detail.total.toLocaleString()}</td>
            `;
            tbody.appendChild(row);
          });

          // Add summary section
          const summarySection = document.createElement("div");
          summarySection.id = "summarySection";
          summarySection.className = "flex justify-end mt-6 mb-4";

          const discountAmount = data.header.potnomfaktur || 0;
          const discountDisplay =
            discountAmount > 0 ? `-${discountAmount.toLocaleString()}` : "-";

          const summaryTable = document.createElement("table");
          summaryTable.className = "text-right";
          summaryTable.innerHTML = `
            <tr>
              <td class="pr-4 text-gray-400">Subtotal:</td>
              <td class="text-gray-300">${total.toLocaleString()}</td>
            </tr>
            <tr>
              <td class="pr-4 text-gray-400">Discount:</td>
              <td class="${
                discountAmount > 0 ? "text-red-400" : "text-gray-300"
              }">${discountDisplay}</td>
            </tr>
            <tr class="font-bold">
              <td class="pr-4 text-gray-400">Total:</td>
              <td class="text-gray-300">${(
                total - discountAmount
              ).toLocaleString()}</td>
            </tr>
          `;

          summarySection.appendChild(summaryTable);
          tbody.parentNode.insertAdjacentElement("afterend", summarySection);

          // Show modal
          const modal = document.getElementById("transactionModal");
          modal.classList.remove("hidden");
          modal.classList.add("flex");
        } catch (error) {
          console.error("Error fetching transaction details:", error);
          alert(error.message || "Failed to load transaction details");
        }
      }

      function openThreeMonthModal() {
        const modal = document.getElementById("threeMonthModal");
        const iframe = document.getElementById("threeMonthFrame");

        // Set the iframe source to the three-month sales page
        iframe.src = "/three-month-sales";

        // Show the modal
        modal.classList.remove("hidden");
        modal.classList.add("flex");

        // Prevent scrolling on the body
        document.body.style.overflow = "hidden";
      }

      function closeThreeMonthModal() {
        const modal = document.getElementById("threeMonthModal");
        const iframe = document.getElementById("threeMonthFrame");

        // Hide the modal
        modal.classList.add("hidden");
        modal.classList.remove("flex");

        // Clear the iframe source to stop any ongoing processes
        setTimeout(() => {
          iframe.src = "";
        }, 100);

        // Re-enable scrolling on the body
        document.body.style.overflow = "";
      }

      // Initialize modal click-outside behavior and close button
      document.addEventListener("DOMContentLoaded", function () {
        // Add click event listeners to transaction IDs
        document
          .querySelectorAll('[onclick*="showTransactionDetails"]')
          .forEach((element) => {
            const match = element.getAttribute("onclick").match(/'([^']+)'/);
            if (match && match[1]) {
              const notransaksi = match[1];
              element.removeAttribute("onclick");
              element.addEventListener("click", function () {
                showTransactionDetails(notransaksi);
              });
            }
          });

        // Add click event listener to close transaction modal when clicking outside
        const modal = document.getElementById("transactionModal");
        if (modal) {
          modal.addEventListener("click", function (e) {
            if (e.target === this) {
              closeTransactionModal();
            }
          });
        }

        // Add click event listener to close three-month modal when clicking outside
        const threeMonthModal = document.getElementById("threeMonthModal");
        if (threeMonthModal) {
          threeMonthModal.addEventListener("click", function (e) {
            if (e.target === this) {
              closeThreeMonthModal();
            }
          });
        }
      });
    </script>
    <!-- Three Month Sales Modal -->
    <div
      id="threeMonthModal"
      class="fixed inset-0 bg-gray-900 bg-opacity-70 hidden items-center justify-center z-50"
    >
      <div
        class="bg-gray-800 rounded-lg shadow-xl border-2 border-blue-500 w-[75%] h-[75vh] max-h-[75vh] flex flex-col"
      >
        <div
          class="flex justify-between items-center p-4 border-b border-gray-700"
        >
          <h3 class="text-xl font-bold text-white">3-Month Sales</h3>
          <button
            onclick="closeThreeMonthModal()"
            class="text-gray-400 hover:text-white"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <div class="flex-grow overflow-hidden p-4">
          <iframe
            id="threeMonthFrame"
            src=""
            class="w-full h-full border-none"
          ></iframe>
        </div>
      </div>
    </div>
    <script>
      // Use the setupChatHistoryEventListeners function from today-sales-details.js
      document.addEventListener("DOMContentLoaded", function () {
        // Call the global function from today-sales-details.js
        if (typeof setupChatHistoryEventListeners === "function") {
          setupChatHistoryEventListeners();
        } else {
          console.error("setupChatHistoryEventListeners function not found");

          // Fallback initialization for copy buttons if the main function is not available
          document.querySelectorAll(".copy-button").forEach((element) => {
            element.addEventListener("click", function (event) {
              event.stopPropagation(); // Prevent triggering parent click events
              const textToCopy = this.getAttribute("data-copy-text");
              console.log("Copy button clicked:", textToCopy);

              // Try to use the global function if it's available now
              if (typeof copyToClipboard === "function") {
                copyToClipboard(textToCopy);
              } else {
                // Manual fallback implementation
                try {
                  // Create a temporary textarea element
                  const textarea = document.createElement("textarea");
                  textarea.value = textToCopy;

                  // Make the textarea out of viewport
                  textarea.style.position = "fixed";
                  textarea.style.left = "-999999px";
                  textarea.style.top = "-999999px";

                  document.body.appendChild(textarea);
                  textarea.focus();
                  textarea.select();

                  // Execute copy command
                  const successful = document.execCommand("copy");
                  document.body.removeChild(textarea);

                  if (successful) {
                    if (typeof Toastify === "function") {
                      Toastify({
                        text: "Product name copied!",
                        duration: 2000,
                        gravity: "bottom",
                        position: "right",
                        backgroundColor: "#4CAF50",
                      }).showToast();
                    } else {
                      alert("Text copied!");
                    }
                  } else {
                    throw new Error("Copy command was unsuccessful");
                  }
                } catch (err) {
                  console.error("Fallback copy failed: ", err);
                  alert("Failed to copy text");
                }
              }
            });
          });
        }
      });
    </script>
  </body>
</html>
