Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAD3FD0000 ntdll.dll
7FFAD23E0000 KERNEL32.DLL
7FFAD1860000 KERNELBASE.dll
7FFAD3D40000 USER32.dll
7FFAD1C30000 win32u.dll
7FFAD3D00000 GDI32.dll
7FFAD1240000 gdi32full.dll
7FFAD1700000 msvcp_win.dll
7FFAD1420000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFAD2E10000 advapi32.dll
7FFAD22D0000 msvcrt.dll
7FFAD21F0000 sechost.dll
7FFAD13F0000 bcrypt.dll
7FFAD2EE0000 RPCRT4.dll
7FFAD0720000 CRYPTBASE.DLL
7FFAD1540000 bcryptPrimitives.dll
7FFAD3F50000 IMM32.DLL
