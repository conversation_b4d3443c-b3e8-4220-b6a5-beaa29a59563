/**
 * Utility functions for opening twelve-month sales analysis
 */

/**
 * Open twelve-month sales page with a specific item code search
 * @param {string} kodeitem - The item code to search for
 * @param {string} target - Target window ('_blank' for new tab, '_self' for same window)
 */
function openTwelveMonthSalesWithItem(kodeitem, target = '_blank') {
    if (!kodeitem) {
        console.error('Item code is required');
        return;
    }
    
    // Encode the item code for URL safety
    const encodedKodeitem = encodeURIComponent(kodeitem);
    const url = `/twelve-month-sales?search=${encodedKodeitem}`;
    
    // Open in specified target
    window.open(url, target);
}

/**
 * Open twelve-month sales page with a search term (item name, etc.)
 * @param {string} searchTerm - The search term to use
 * @param {string} target - Target window ('_blank' for new tab, '_self' for same window)
 */
function openTwelveMonthSalesWithSearch(searchTerm, target = '_blank') {
    if (!searchTerm) {
        console.error('Search term is required');
        return;
    }
    
    // Encode the search term for URL safety
    const encodedSearchTerm = encodeURIComponent(searchTerm);
    const url = `/twelve-month-sales?search=${encodedSearchTerm}`;
    
    // Open in specified target
    window.open(url, target);
}

/**
 * Open twelve-month sales page without any search (shows search form)
 * @param {string} target - Target window ('_blank' for new tab, '_self' for same window)
 */
function openTwelveMonthSales(target = '_blank') {
    const url = '/twelve-month-sales';
    window.open(url, target);
}

/**
 * Add twelve-month sales button to an element
 * @param {HTMLElement} element - The element to add the button to
 * @param {string} kodeitem - The item code
 * @param {string} title - Optional title for the button
 */
function addTwelveMonthSalesButton(element, kodeitem, title = 'View 12-month sales analysis') {
    const button = document.createElement('button');
    button.className = 'ml-1 text-gray-400 hover:text-green-400 transition-colors duration-150';
    button.title = title;
    button.onclick = function(e) {
        e.preventDefault();
        e.stopPropagation();
        openTwelveMonthSalesWithItem(kodeitem);
    };
    
    button.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
    `;
    
    element.appendChild(button);
    return button;
}

/**
 * Make the "Sales: ..." text clickable to open twelve-month sales
 * @param {string} selector - CSS selector for the sales element
 */
function makeSalesClickable(selector = '[id*="Sales"], .sales-text') {
    const salesElements = document.querySelectorAll(selector);
    salesElements.forEach(element => {
        element.style.cursor = 'pointer';
        element.title = 'Click to view 12-month sales analysis';
        element.addEventListener('click', function(e) {
            e.preventDefault();
            openTwelveMonthSales();
        });
    });
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Make sales text clickable if it exists
    makeSalesClickable();
});
