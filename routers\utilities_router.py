from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import <PERSON><PERSON><PERSON>Templates
import subprocess
import os
from datetime import datetime
import re

router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/utilities", response_class=HTMLResponse)
async def get_utilities_page(request: Request):
    return templates.TemplateResponse("utilities.html", {"request": request})

# @router.post("/wol-toko")
# async def run_wol_toko():
#     try:
#         # Path to the WOL Toko Python file
#         wol_script_path = r"H:\Python\FinalApp\WakeOnLanWOL_Toko.py"
        
#         # Check if file exists
#         if not os.path.exists(wol_script_path):
#             return JSONResponse(
#                 status_code=404,
#                 content={"success": False, "message": "WOL Toko script not found"}
#             )
        
#         # Execute the Python script
#         result = subprocess.run(
#             ["python", wol_script_path],
#             capture_output=True,
#             text=True,
#             timeout=50
#         )
        
#         if result.returncode == 0:
#             return JSONResponse(
#                 content={
#                     "success": True, 
#                     "message": "WOL Toko executed successfully",
#                     "output": result.stdout
#                 }
#             )
#         else:
#             return JSONResponse(
#                 status_code=500,
#                 content={
#                     "success": False, 
#                     "message": "WOL Toko execution failed",
#                     "error": result.stderr
#                 }
#             )
            
#     except subprocess.TimeoutExpired:
#         return JSONResponse(
#             status_code=408,
#             content={"success": False, "message": "WOL Toko execution timed out"}
#         )
#     except Exception as e:
#         return JSONResponse(
#             status_code=500,
#             content={"success": False, "message": f"Error executing WOL Toko: {str(e)}"}
#         )

@router.get("/wol-toko-page", response_class=HTMLResponse)
async def get_wol_toko_page(request: Request):
    """Display WOL Toko execution results in a new page"""
    try:
        # Path to the WOL Toko Python file
        wol_script_path = r"H:\Python\FinalApp\WakeOnLanWOL_Toko.py"
        
        # Check if file exists
        if not os.path.exists(wol_script_path):
            return templates.TemplateResponse("wol_output.html", {
                "request": request,
                "success": False,
                "message": "WOL Toko script not found",
                "output": "",
                "error": f"Script not found at: {wol_script_path}"
            })
        
        # Execute the Python script
        result = subprocess.run(
            ["python", wol_script_path],
            capture_output=True,
            text=True,
            timeout=50
        )
        
        if result.returncode == 0:
            return templates.TemplateResponse("wol_output.html", {
                "request": request,
                "success": True,
                "message": "WOL Toko executed successfully",
                "output": result.stdout,
                "error": ""
            })
        else:
            return templates.TemplateResponse("wol_output.html", {
                "request": request,
                "success": False,
                "message": "WOL Toko execution failed",
                "output": result.stdout,
                "error": result.stderr
            })
            
    except subprocess.TimeoutExpired:
        return templates.TemplateResponse("wol_output.html", {
            "request": request,
            "success": False,
            "message": "WOL Toko execution timed out",
            "output": "",
            "error": "Script execution timed out after 50 seconds"
        })
    except Exception as e:
        return templates.TemplateResponse("wol_output.html", {
            "request": request,
            "success": False,
            "message": f"Error executing WOL Toko: {str(e)}",
            "output": "",
            "error": str(e)
        })

@router.get("/check-log")
async def check_log():
    """Check the latest log date and return last 10 lines from playwright_log.txt"""
    try:
        log_file_path = r"H:\Python\Telegram Todo\playwright_log.txt"
        
        # Check if file exists
        if not os.path.exists(log_file_path):
            return JSONResponse(
                status_code=404,
                content={
                    "success": False, 
                    "message": "Log file not found",
                    "path": log_file_path
                }
            )
        
        # Read the file
        with open(log_file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
        
        if not lines:
            return JSONResponse(
                content={
                    "success": True,
                    "message": "Log file is empty",
                    "latest_date": None,
                    "last_10_lines": [],
                    "total_lines": 0
                }
            )
        
        # Find the latest date in the file
        latest_date = None
        date_pattern = r'\d{4}-\d{2}-\d{2}|\d{2}/\d{2}/\d{4}|\d{2}-\d{2}-\d{4}'
        
        # Search from bottom to top for the most recent date
        for line in reversed(lines):
            date_matches = re.findall(date_pattern, line)
            if date_matches:
                latest_date = date_matches[0]
                break
        
        # Find the latest error log entry
        latest_error = None
        error_context = []
        
        # Search from bottom to top for the most recent ERROR entry
        for i in range(len(lines) - 1, -1, -1):
            line = lines[i].strip()
            if 'ERROR' in line and re.search(r'\d{4}-\d{2}-\d{2}', line):
                # Found an error line, collect it and subsequent context lines
                error_context = [line]
                
                # Collect following lines that might be part of the error (indented or continuation)
                for j in range(i + 1, min(i + 10, len(lines))):
                    next_line = lines[j].strip()
                    # Stop if we hit another timestamp or empty line
                    if re.search(r'\d{4}-\d{2}-\d{2}', next_line) and ('INFO' in next_line or 'ERROR' in next_line or 'WARNING' in next_line):
                        break
                    if next_line:  # Only add non-empty lines
                        error_context.append(next_line)
                    elif len(error_context) > 1:  # Stop at empty line if we already have content
                        break
                
                latest_error = '\n'.join(error_context)
                break
        
        # Find the latest success message (Message processed)
        latest_success_message = None
        for line in reversed(lines):
            if 'INFO' in line and 'Message processed:' in line:
                latest_success_message = line.strip()
                break
        
        # Find the latest session run
        latest_session_run = None
        for line in reversed(lines):
            if 'INFO' in line and '=== Starting new session ===' in line:
                latest_session_run = line.strip()
                break
        
        return JSONResponse(
            content={
                "success": True,
                "message": "Log file checked successfully",
                "latest_date": latest_date,
                "latest_error": latest_error,
                "latest_success_message": latest_success_message,
                "latest_session_run": latest_session_run,
                "total_lines": len(lines),
                "file_path": log_file_path
            }
        )
        
    except Exception as e:
        return JSONResponse(
             status_code=500,
             content={
                 "success": False, 
                 "message": f"Error reading log file: {str(e)}",
                 "error": str(e)
             }
         )

@router.post("/open-log-file")
async def open_log_file():
    """Open the playwright_log.txt file with the default application"""
    try:
        log_file_path = r"H:\Python\Telegram Todo\playwright_log.txt"
        
        # Check if file exists
        if not os.path.exists(log_file_path):
            return JSONResponse(
                status_code=404,
                content={
                    "success": False, 
                    "message": "Log file not found",
                    "path": log_file_path
                }
            )
        
        # Open file with default application (Windows)
        result = subprocess.run(
            ["start", "", log_file_path],
            shell=True,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            return JSONResponse(
                content={
                    "success": True,
                    "message": "Log file opened successfully",
                    "file_path": log_file_path
                }
            )
        else:
            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "message": "Failed to open log file",
                    "error": result.stderr
                }
            )
            
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "success": False, 
                "message": f"Error opening log file: {str(e)}",
                "error": str(e)
            }
        )