from fastapi import APIRouter, Depends, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy import func, and_
from sqlalchemy.orm import Session
from datetime import datetime, timedelta, date
import calendar
import pytz
from decimal import Decimal
from database.database_connection import get_db
from database.database_model import ItemSalesHeader, ItemSalesDetail, ItemUnitConversion

router = APIRouter()
templates = Jinja2Templates(directory="templates")

def currency_filter(value):
    try:
        return f"Rp {int(value):,}"
    except (ValueError, TypeError):
        return "Rp 0"

templates.env.filters["currency"] = currency_filter

def get_daily_profit_data(db: Session, month: int, year: int):
    """Helper function to get daily profit data from a database session"""
    jakarta_tz = pytz.timezone('Asia/Jakarta')
    
    # Get number of days in the month
    _, num_days = calendar.monthrange(year, month)
    daily_data = []
    
    for day in range(1, num_days + 1):
        start_date = datetime(year, month, day, tzinfo=jakarta_tz)
        end_date = start_date + timedelta(days=1)

        # Get gross sales
        sales = db.query(
            func.sum(ItemSalesHeader.totalakhir).label('gross_sales')
        ).filter(
            ItemSalesHeader.tanggal >= start_date,
            ItemSalesHeader.tanggal < end_date,
            ItemSalesHeader.tipe == 'KSR'
        ).scalar() or 0

        # Calculate total cost and net profit
        sales_data = db.query(
            func.sum(ItemSalesDetail.total).label('total_sales'),
            func.sum(ItemUnitConversion.hargapokok * ItemSalesDetail.jumlah).label('total_cost')
        ).join(
            ItemSalesHeader,
            ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
        ).outerjoin(
            ItemUnitConversion,
            and_(
                ItemSalesDetail.kodeitem == ItemUnitConversion.kodeitem,
                ItemSalesDetail.satuan == ItemUnitConversion.satuan
            )
        ).filter(
            ItemSalesHeader.tanggal >= start_date,
            ItemSalesHeader.tanggal < end_date,
            ItemSalesHeader.tipe == 'KSR'
        ).first()

        total_sales = float(sales_data[0] or 0)
        total_cost = float(sales_data[1] or 0)
        profit = total_sales - total_cost
        
        # Only append if there were sales on this day
        if total_sales > 0:
            margin = (profit / total_sales * 100) if total_sales > 0 else 0
            daily_data.append({
                'date': start_date.strftime('%d %b %Y'),
                'total_sales': total_sales,
                'total_cost': total_cost,
                'profit': profit,
                'margin': margin
            })
    
    return daily_data

@router.get("/daily-profit", response_class=HTMLResponse)
async def get_daily_profit(
    request: Request, 
    month: int = None, 
    year: int = None, 
    db: Session = Depends(get_db)
):
    jakarta_tz = pytz.timezone('Asia/Jakarta')
    current_date = datetime.now(jakarta_tz)
    
    # Use provided month/year or default to current
    year = year or current_date.year
    month = month or current_date.month
    
    # Calculate previous and next month/year
    if month == 1:
        prev_month, prev_year = 12, year - 1
    else:
        prev_month, prev_year = month - 1, year
        
    if month == 12:
        next_month, next_year = 1, year + 1
    else:
        next_month, next_year = month + 1, year

    # Try to get data from current database first
    daily_data = get_daily_profit_data(db, month, year)
    
    # If no data found in current database, try old database
    if not daily_data:
        from database.database_connection import get_db
        old_db_gen = get_db("old")
        old_db = next(old_db_gen)
        try:
            daily_data = get_daily_profit_data(old_db, month, year)
        finally:
            old_db.close()

    return templates.TemplateResponse(
        "daily_profit.html",
        {
            "request": request,
            "daily_data": daily_data,
            "current_month": month,
            "current_year": year,
            "prev_month": prev_month,
            "prev_year": prev_year,
            "next_month": next_month,
            "next_year": next_year,
            "month_name": calendar.month_name[month]
        }
    )
