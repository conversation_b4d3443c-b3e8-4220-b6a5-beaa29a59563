from fastapi import APIRouter, Depends, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from sqlalchemy import func, or_, and_
from datetime import datetime
from database.database_connection import get_db
from database.database_model import (
    Item,
    ItemUnitConversion,
    ItemStock,
    ItemSellingPrice,
    ItemSalesHeader
)
import pytz
import pandas as pd
import os
from shared_state import search_history  # Import from shared state

router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/", response_class=HTMLResponse)
async def get_landing_page(
    request: Request,
    search: str = None,
    negative: str = None,  # Changed from bool to str to handle form submission better
    db: Session = Depends(get_db)
):
    search_key = None
    items_data = []

    # Convert negative parameter to boolean
    is_negative_search = negative == 'true'

    if search or is_negative_search:
        # Base query joining all necessary tables
        query = db.query(
            Item,
            ItemStock.stok,
            ItemUnitConversion.satuan,
            ItemUnitConversion.hargapokok,
            ItemUnitConversion.jumlahkonv,
            ItemUnitConversion.tipe,
            ItemSellingPrice.hargajual
        )\
        .join(ItemStock, Item.kodeitem == ItemStock.kodeitem, isouter=True)\
        .join(ItemUnitConversion, Item.kodeitem == ItemUnitConversion.kodeitem, isouter=True)\
        .join(ItemSellingPrice,
              (Item.kodeitem == ItemSellingPrice.kodeitem) &
              (ItemUnitConversion.satuan == ItemSellingPrice.satuan),
              isouter=True)

        if is_negative_search:
            # Filter for items with stock <= 0
            query = query.filter(ItemStock.stok < 0)
        elif search:
            # Split search terms by 'not' keyword
            parts = search.lower().split(' not ')

            # Get positive terms (before 'not')
            positive_terms = parts[0].split()

            # Get negative terms (after 'not')
            negative_terms = parts[1].split() if len(parts) > 1 else []

            # Apply positive search filters
            for term in positive_terms:
                query = query.filter(
                    or_(
                        func.lower(Item.kodeitem).like(f"%{term}%"),
                        func.lower(Item.namaitem).like(f"%{term}%"),
                        func.lower(Item.jenis).like(f"%{term}%"),
                        func.lower(Item.rak).like(f"%{term}%"),
                        func.lower(Item.keterangan).like(f"%{term}%"),
                        func.lower(Item.supplier1).like(f"%{term}%"),
                        func.lower(Item.merek).like(f"%{term}%"),
                        func.lower(ItemUnitConversion.kodebarcode).like(f"%{term}%")
                    )
                )

            # Print result size
            result_size = query.count()
            print(f"Number of results after applying positive filters: {result_size}")

            # Apply negative search filters
            if negative_terms:
                negative_conditions = []
                for term in negative_terms:
                    negative_conditions.append(
                        and_(
                            or_(
                                Item.kodeitem.is_(None),
                                ~func.lower(Item.kodeitem).like(f"%{term}%")
                            ),
                            or_(
                                Item.namaitem.is_(None),
                                ~func.lower(Item.namaitem).like(f"%{term}%")
                            ),
                            or_(
                                Item.jenis.is_(None),
                                ~func.lower(Item.jenis).like(f"%{term}%")
                            ),
                            or_(
                                Item.rak.is_(None),
                                ~func.lower(Item.rak).like(f"%{term}%")
                            ),
                            or_(
                                Item.keterangan.is_(None),
                                ~func.lower(Item.keterangan).like(f"%{term}%")
                            ),
                            or_(
                                Item.supplier1.is_(None),
                                Item.supplier1 == '',
                                ~func.lower(Item.supplier1).like(f"%{term}%")
                            ),
                            or_(
                                Item.merek.is_(None),
                                ~func.lower(Item.merek).like(f"%{term}%")
                            )

                        )
                    )
                query = query.filter(and_(*negative_conditions))

            # Print result size after applying negative filters
            result_size = query.count()
            print(f"Number of results after applying all filters: {result_size}")

        # Apply order by and get results
        results = query.order_by(Item.kodeitem.asc()).all()
        print(f"Number of results: {len(results)}")

        # Process the results and create DataFrame
        rows = []
        items_dict = {}

        for result in results:
            item, stok, satuan, hargapokok, jumlahkonv, tipe, hargajual = result

            if item.kodeitem not in items_dict:
                items_dict[item.kodeitem] = {
                    'item': item,
                    'stok': stok if stok is not None else 0,
                    'units': [],
                }

            if satuan and tipe:
                current_stok = items_dict[item.kodeitem]['stok'] or 0
                unit_info = {
                    'satuan': satuan,
                    'tipe': tipe,
                    'hargapokok': hargapokok,
                    'jumlahkonv': jumlahkonv if jumlahkonv is not None else 1,
                    'hargajual': hargajual,
                    'stok_in_unit': (current_stok / jumlahkonv) if jumlahkonv and jumlahkonv != 0 else current_stok
                }
                items_dict[item.kodeitem]['units'].append(unit_info)

                # Create a row for DataFrame
                rows.append({
                    'kodeitem': item.kodeitem,
                    'namaitem': item.namaitem,
                    'stok': current_stok,
                    'stokmin': item.stokmin,  # Add this line
                    'merek': item.merek,
                    'jenis': item.jenis,
                    'satuan': satuan,
                    'tipe': tipe,
                    'hargapokok': hargapokok,
                    'hargajual': hargajual,
                    'rak': item.rak,
                    'keterangan': item.keterangan,
                    'supplier': item.supplier1,
                    'stok_in_unit': (current_stok / jumlahkonv) if jumlahkonv and jumlahkonv != 0 else current_stok
                })

        items_data = list(items_dict.values())

        if rows:
            df = pd.DataFrame(rows)
            search_key = f"search_{datetime.now(pytz.timezone('Asia/Jakarta')).strftime('%Y%m%d_%H%M%S')}"
            search_history[search_key] = (df, search if search else "negative_stock")

            # Keep only the last 10 searches
            if len(search_history) > 10:
                oldest_key = min(search_history.keys())
                _, old_search = search_history[oldest_key]
                try:
                    old_files = [f for f in os.listdir(r"E:\Download") if f.startswith(f"search_{old_search}_")]
                    for old_file in old_files:
                        os.remove(os.path.join(r"E:\Download", old_file))
                except:
                    pass
                del search_history[oldest_key]

    # Get today's total sales
    jakarta_tz = pytz.timezone('Asia/Jakarta')
    today = datetime.now(jakarta_tz).date()

    try:
        # Calculate today's sales with error handling
        total_sales = db.query(func.sum(ItemSalesHeader.totalakhir))\
            .filter(
                func.date(ItemSalesHeader.tanggal) == today,
                ItemSalesHeader.tipe == 'KSR'
            ).scalar() or 0
    except Exception as e:
        print(f"Database error in landing page: {e}")
        total_sales = 0  # Fallback to zero if database is unavailable

    # Calculate counts
    if search_key and search_key in search_history:
        df, _ = search_history[search_key]
        total_rows = len(df)
        total_items = df['kodeitem'].nunique()
    else:
        total_rows = 0
        total_items = 0

    return templates.TemplateResponse(
        "landing_page.html",
        {
            "request": request,
            "items": items_data,
            "search": search,
            "negative": is_negative_search,  # Pass the boolean value
            "total_sales": total_sales,
            "datetime": datetime,
            "pytz": pytz,
            "last_search_key": search_key if search or is_negative_search else None,
            "total_rows": total_rows,
            "total_items": total_items
        }
    )

@router.get("/search-history/{search_key}")
async def get_search_history(search_key: str):
    if search_key in search_history:
        return JSONResponse(content=search_history[search_key][0].to_dict(orient='records'))
    return JSONResponse(content={"error": "Search not found"}, status_code=404)

# Add custom filter to convert price to thousands
def format_price_in_thousands(value):
    if value is None:
        return "0"
    return f"{float(value) / 1000:.1f}".rstrip('0').rstrip('.')

# Add custom filter to limit string length
def limit_string(value, length):
    if value is None:
        return ""
    return (value[:length] + '...') if len(value) > length else value

# Register the custom filters
templates.env.filters["thousands"] = format_price_in_thousands
templates.env.filters["limit"] = limit_string
