<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Record Marketplace Purchases</title>
    <link rel="icon" type="image/x-icon" href="/static/img/favicon.ico" />
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <style>
      .bg-gray-750 {
        background-color: #4b5563;
      }
      .bg-gray-650 {
        background-color: #374151;
      }
    </style>
  </head>
  <body class="bg-gray-900 text-white min-h-screen">
    <div class="container mx-auto px-4 py-8">
      <!-- Header -->
      <div class="flex items-center justify-between mb-8">
        <h1 class="text-3xl font-bold text-blue-400">
          <i class="fas fa-shopping-cart mr-3"></i>Record Marketplace Purchases
        </h1>
        <button
          onclick="goBack()"
          class="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg transition-colors"
        >
          <i class="fas fa-arrow-left mr-2"></i>Back
        </button>
      </div>

      <!-- Main Form -->
      <div class="bg-gray-800 rounded-lg p-6 mb-8">
        <!-- Date Picker -->
        <div class="mb-6">
          <label for="purchaseDate" class="block text-sm font-medium mb-2"
            >Purchase Date:</label
          >
          <input
            type="date"
            id="purchaseDate"
            class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 w-full md:w-auto focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <!-- Search Item Section -->
        <div class="mb-6">
          <label for="itemSearch" class="block text-sm font-medium mb-2"
            >Search Item:</label
          >
          <div class="relative">
            <input
              type="text"
              id="itemSearch"
              placeholder="Type item name or code..."
              class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <div
              id="searchResults"
              class="absolute z-10 w-full bg-gray-700 border border-gray-600 rounded-lg mt-1 hidden max-h-60 overflow-y-auto"
            ></div>
          </div>

          <!-- Manual Item Entry -->
          <div class="mt-4 p-4 bg-gray-700 rounded-lg">
            <h4 class="text-sm font-medium mb-3">Or Add New Item:</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <input
                type="text"
                id="manualItemName"
                placeholder="Item name"
                class="bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <input
                type="number"
                id="manualQuantity"
                placeholder="Quantity"
                step="1"
                class="bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                onclick="addManualItem()"
                class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded transition-colors"
              >
                <i class="fas fa-plus mr-2"></i>Add Item
              </button>
            </div>
            <div class="border-t border-gray-600 pt-4">
              <h5 class="text-sm font-medium mb-3">Import Multiple Items:</h5>
              <button
                onclick="importFromClipboard()"
                class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded transition-colors w-full"
              >
                <i class="fas fa-clipboard mr-2"></i>Import from Clipboard
              </button>
              <p class="text-xs text-gray-400 mt-2">
                Supports two formats:<br />1. Excel format: Item name and
                quantity on same line (tab/space separated)<br />2. Line format:
                Item name on one line, quantity on next line
              </p>
            </div>
          </div>
        </div>

        <!-- Added Items List -->
        <div class="mb-6">
          <h3 class="text-lg font-medium mb-4">Added Items:</h3>
          <div class="bg-gray-700 rounded-lg overflow-hidden">
            <table class="w-full">
              <thead class="bg-gray-600">
                <tr>
                  <th class="px-4 py-3 text-left">Item Name</th>
                  <th class="px-4 py-3 text-left">Quantity</th>
                  <th class="px-4 py-3 text-left">Received</th>
                  <th class="px-4 py-3 text-left">Actions</th>
                </tr>
              </thead>
              <tbody id="addedItemsList">
                <tr id="noItemsRow">
                  <td colspan="4" class="px-4 py-8 text-center text-gray-400">
                    No items added yet
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-4">
          <button
            onclick="savePurchases()"
            class="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-lg transition-colors"
            id="saveBtn"
          >
            <i class="fas fa-save mr-2"></i>Save Purchases
          </button>
          <button
            onclick="clearForm()"
            class="bg-gray-600 hover:bg-gray-700 px-6 py-2 rounded-lg transition-colors"
          >
            <i class="fas fa-trash mr-2"></i>Clear Form
          </button>
        </div>
      </div>

      <!-- Past Purchases -->
      <div class="bg-gray-800 rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium">Past Purchases:</h3>
          <button
            onclick="loadPastPurchases()"
            class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors"
          >
            <i class="fas fa-refresh mr-2"></i>Refresh
          </button>
        </div>
        <div class="bg-gray-700 rounded-lg overflow-hidden">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-600">
                <tr>
                  <th class="px-4 py-3 text-left">Item</th>
                  <th class="px-4 py-3 text-left">Qty</th>
                  <th class="px-4 py-3 text-left">Date</th>
                  <th class="px-4 py-3 text-left">Received</th>
                  <th class="px-4 py-3 text-left">Actions</th>
                </tr>
              </thead>
              <tbody id="pastPurchasesList">
                <tr>
                  <td colspan="5" class="px-4 py-8 text-center text-gray-400">
                    Loading past purchases...
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div
      id="loadingOverlay"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50"
    >
      <div class="bg-gray-800 rounded-lg p-6 text-center">
        <i class="fas fa-spinner fa-spin text-3xl text-blue-400 mb-4"></i>
        <p>Processing...</p>
      </div>
    </div>

    <script>
      let addedItems = [];
      let searchTimeout;

      // Initialize page
      document.addEventListener("DOMContentLoaded", function () {
        // Set today's date as default
        document.getElementById("purchaseDate").value = new Date()
          .toISOString()
          .split("T")[0];
        loadPastPurchases();
      });

      // Search functionality
      document
        .getElementById("itemSearch")
        .addEventListener("input", function () {
          clearTimeout(searchTimeout);
          const query = this.value.trim();

          if (query.length < 2) {
            hideSearchResults();
            return;
          }

          searchTimeout = setTimeout(() => searchItems(query), 300);
        });

      async function searchItems(query) {
        try {
          const response = await fetch(
            `/api/search-items?q=${encodeURIComponent(query)}`
          );
          const items = await response.json();
          displaySearchResults(items);
        } catch (error) {
          console.error("Search error:", error);
          showNotification("Search failed", "error");
        }
      }

      function displaySearchResults(items) {
        const resultsDiv = document.getElementById("searchResults");

        if (items.length === 0) {
          resultsDiv.innerHTML =
            '<div class="p-3 text-gray-400">No items found</div>';
        } else {
          resultsDiv.innerHTML = items
            .map((item, index) => {
              const escapedKodeitem = item.kodeitem
                .replace(/'/g, "\\'")
                .replace(/"/g, '\\"');
              const escapedNamaitem = item.namaitem
                .replace(/'/g, "\\'")
                .replace(/"/g, '\\"');
              const escapedSatuan = item.satuan
                .replace(/'/g, "\\'")
                .replace(/"/g, '\\"');

              return `
                        <div class="p-3 hover:bg-gray-600 cursor-pointer border-b border-gray-600 last:border-b-0" data-index="${index}" onclick="selectItemByIndex(${index})">
                            <div class="font-medium">${item.namaitem}</div>
                            <div class="text-sm text-gray-400">${item.kodeitem} - ${item.jenis} (${item.satuan})</div>
                        </div>
                    `;
            })
            .join("");
        }

        resultsDiv.classList.remove("hidden");

        // Store items globally for selection
        window.searchResultItems = items;
      }

      function selectItemByIndex(index) {
        const item = window.searchResultItems[index];
        if (item) {
          selectItem(item.kodeitem, item.namaitem, item.satuan);
        }
      }

      function hideSearchResults() {
        document.getElementById("searchResults").classList.add("hidden");
      }

      function selectItem(kodeitem, namaitem, satuan) {
        showQuantityModal(namaitem, satuan, (quantity) => {
          if (quantity && !isNaN(quantity) && parseFloat(quantity) > 0) {
            addItemToList({
              kodeitem: kodeitem,
              item_name: namaitem,
              quantity: parseFloat(quantity),
              received: false,
            });
          }
        });

        document.getElementById("itemSearch").value = "";
        hideSearchResults();
      }

      function showQuantityModal(itemName, satuan, callback) {
        const modal = document.createElement("div");
        modal.className =
          "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50";
        modal.id = "quantityModal";

        const modalContent = document.createElement("div");
        modalContent.className =
          "bg-gray-800 rounded-lg p-6 min-w-80 max-w-md mx-4";

        modalContent.innerHTML = `
                <h3 class="text-lg font-medium mb-4 text-white">Enter Quantity</h3>
                <p class="text-gray-300 mb-4">Item: ${itemName} (${satuan})</p>
                <input type="number" id="quantityInput" placeholder="Enter quantity" min="0.01" step="1" 
                       class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4">
                <div class="flex justify-end gap-3">
                    <button onclick="closeQuantityModal()" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors text-white">Cancel</button>
                    <button onclick="confirmQuantity()" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors text-white">Add</button>
                </div>
            `;

        modal.appendChild(modalContent);
        document.body.appendChild(modal);

        // Focus on input
        setTimeout(() => {
          document.getElementById("quantityInput").focus();
        }, 100);

        // Store callback for global access
        window.quantityCallback = callback;

        // Handle Enter key
        document
          .getElementById("quantityInput")
          .addEventListener("keypress", function (e) {
            if (e.key === "Enter") {
              confirmQuantity();
            }
          });
      }

      function closeQuantityModal() {
        const modal = document.getElementById("quantityModal");
        if (modal) {
          modal.remove();
        }
        window.quantityCallback = null;
      }

      function confirmQuantity() {
        const quantity = document.getElementById("quantityInput").value;
        if (window.quantityCallback) {
          window.quantityCallback(quantity);
        }
        closeQuantityModal();
      }

      function addManualItem() {
        const itemName = document.getElementById("manualItemName").value.trim();
        const quantity = document.getElementById("manualQuantity").value;

        if (
          !itemName ||
          !quantity ||
          isNaN(quantity) ||
          parseFloat(quantity) <= 0
        ) {
          showNotification(
            "Please enter valid item name and quantity",
            "error"
          );
          return;
        }

        addItemToList({
          kodeitem: null,
          item_name: itemName,
          quantity: parseFloat(quantity),
          received: false,
        });

        // Clear manual entry fields
        document.getElementById("manualItemName").value = "";
        document.getElementById("manualQuantity").value = "";
      }

      function addItemToList(item) {
        addedItems.push(item);
        renderAddedItems();
      }

      function renderAddedItems() {
        const tbody = document.getElementById("addedItemsList");

        if (addedItems.length === 0) {
          tbody.innerHTML = `
                    <tr id="noItemsRow">
                        <td colspan="4" class="px-4 py-8 text-center text-gray-400">
                            No items added yet
                        </td>
                    </tr>
                `;
          return;
        }

        const itemsHtml = addedItems
          .map(
            (item, index) => `
                <tr class="border-b border-gray-600">
                    <td class="px-4 py-3">
                        ${item.item_name}
                        ${
                          item.kodeitem
                            ? `<div class="text-sm text-gray-400">${item.kodeitem}</div>`
                            : ''
                        }
                    </td>
                    <td class="px-4 py-3">${item.quantity}</td>
                    <td class="px-4 py-3">
                        <label class="flex items-center">
                            <input type="checkbox" ${
                              item.received ? "checked" : ""
                            } onchange="toggleReceived(${index})" class="mr-2">
                            <span class="${
                              item.received ? "text-green-400" : "text-gray-400"
                            }">${item.received ? "Yes" : "No"}</span>
                        </label>
                    </td>
                    <td class="px-4 py-3">
                        <button onclick="removeItem(${index})" class="text-red-400 hover:text-red-300">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `
          )
          .join("");

        tbody.innerHTML = itemsHtml;
      }

      function toggleReceived(index) {
        addedItems[index].received = !addedItems[index].received;
        renderAddedItems();
      }

      function removeItem(index) {
        addedItems.splice(index, 1);
        renderAddedItems();
      }

      async function savePurchases() {
        if (addedItems.length === 0) {
          showNotification("Please add at least one item", "error");
          return;
        }

        const purchaseDate = document.getElementById("purchaseDate").value;
        if (!purchaseDate) {
          showNotification("Please select a purchase date", "error");
          return;
        }

        // Create loading indicator if it doesn't exist
        let loadingElement = document.getElementById("loading");
        if (!loadingElement) {
          loadingElement = document.createElement("div");
          loadingElement.id = "loading";
          loadingElement.className =
            "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden";
          loadingElement.innerHTML = `
                    <div class="bg-gray-800 rounded-lg p-6 text-white">
                        <div class="flex items-center gap-3">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                            <span>Saving purchases...</span>
                        </div>
                    </div>
                `;
          document.body.appendChild(loadingElement);
        }

        showLoading(true);

        try {
          const response = await fetch("/api/marketplace-purchases", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              purchase_date: purchaseDate,
              items: addedItems,
            }),
          });

          const result = await response.json();

          if (result.success) {
            showNotification(result.message, "success");
            clearForm();
            loadPastPurchases();
          } else {
            showNotification(
              result.message || "Failed to save purchases",
              "error"
            );
          }
        } catch (error) {
          console.error("Save error:", error);
          showNotification("Failed to save purchases", "error");
        } finally {
          showLoading(false);
        }
      }

      function clearForm() {
        addedItems = [];
        renderAddedItems();
        document.getElementById("itemSearch").value = "";
        document.getElementById("manualItemName").value = "";
        document.getElementById("manualQuantity").value = "";
        hideSearchResults();
      }

      async function importFromClipboard() {
        try {
          const clipboardText = await navigator.clipboard.readText();
          const lines = clipboardText
            .trim()
            .split("\n")
            .map((line) => line.trim())
            .filter((line) => line.length > 0);

          if (lines.length === 0) {
            showNotification("Clipboard is empty", "error");
            return;
          }

          let importedCount = 0;
          let items = [];

          // Check if it's tab/space separated format (Excel format)
          const firstLine = lines[0];
          const hasTabOrMultipleSpaces =
            firstLine.includes("\t") || /\s{2,}/.test(firstLine);

          if (hasTabOrMultipleSpaces) {
            // Excel format: item name and quantity on same line, separated by tabs or multiple spaces
            for (const line of lines) {
              // Split by tab or multiple spaces
              const parts = line
                .split(/\t|\s{2,}/)
                .map((part) => part.trim())
                .filter((part) => part.length > 0);

              if (parts.length >= 2) {
                const itemName = parts[0];
                const quantityStr = parts[parts.length - 1]; // Take the last part as quantity
                const quantity = parseFloat(quantityStr);

                if (!isNaN(quantity) && quantity > 0) {
                  items.push({ name: itemName, quantity: quantity });
                } else {
                  showNotification(
                    `Invalid quantity "${quantityStr}" for item "${itemName}"`,
                    "error"
                  );
                }
              }
            }
          } else {
            // Original format: item name and quantity on separate lines
            if (lines.length % 2 !== 0) {
              showNotification(
                "Invalid format: Each item must have a name and quantity on separate lines",
                "error"
              );
              return;
            }

            for (let i = 0; i < lines.length; i += 2) {
              const itemName = lines[i];
              const quantityStr = lines[i + 1];
              const quantity = parseFloat(quantityStr);

              if (!isNaN(quantity) && quantity > 0) {
                items.push({ name: itemName, quantity: quantity });
              } else {
                showNotification(
                  `Invalid quantity "${quantityStr}" for item "${itemName}"`,
                  "error"
                );
              }
            }
          }

          // Process all valid items
          for (const item of items) {
            // Check if item already exists in the list
            const existingIndex = addedItems.findIndex(
              (existingItem) =>
                existingItem.item_name.toLowerCase() === item.name.toLowerCase()
            );

            if (existingIndex >= 0) {
              // Update existing item quantity
              addedItems[existingIndex].quantity = item.quantity;
            } else {
              // Add new item
              addedItems.push({
                item_name: item.name,
                quantity: item.quantity,
                received: false,
                kodeitem: null, // New item, no code
              });
            }

            importedCount++;
          }

          if (importedCount > 0) {
            renderAddedItems();
            showNotification(
              `Successfully imported ${importedCount} items`,
              "success"
            );
          } else {
            showNotification("No valid items found to import", "error");
          }
        } catch (error) {
          console.error("Clipboard import error:", error);
          if (error.name === "NotAllowedError") {
            showNotification(
              "Clipboard access denied. Please allow clipboard permissions.",
              "error"
            );
          } else {
            showNotification("Failed to read from clipboard", "error");
          }
        }
      }

      async function loadPastPurchases() {
        try {
          const response = await fetch("/api/marketplace-purchases");
          const purchases = await response.json();
          renderPastPurchases(purchases);
        } catch (error) {
          console.error("Load error:", error);
          document.getElementById("pastPurchasesList").innerHTML =
            '<tr><td colspan="5" class="px-4 py-8 text-center text-red-400">Failed to load past purchases</td></tr>';
        }
      }

      function renderPastPurchases(purchases) {
        const tbody = document.getElementById("pastPurchasesList");

        if (purchases.length === 0) {
          tbody.innerHTML =
            '<tr><td colspan="5" class="px-4 py-8 text-center text-gray-400">No past purchases found</td></tr>';
          return;
        }

        // Group purchases by date
        const groupedPurchases = {};
        purchases.forEach((purchase) => {
          const date = purchase.purchase_date;
          if (!groupedPurchases[date]) {
            groupedPurchases[date] = [];
          }
          groupedPurchases[date].push(purchase);
        });

        // Sort dates in descending order
        const sortedDates = Object.keys(groupedPurchases).sort(
          (a, b) => new Date(b) - new Date(a)
        );

        let purchasesHtml = "";
        let dateGroupIndex = 0;

        sortedDates.forEach((date) => {
          const isEvenGroup = dateGroupIndex % 2 === 0;
          const bgColor = isEvenGroup ? "bg-gray-750" : "bg-gray-650";

          groupedPurchases[date].forEach((purchase) => {
            purchasesHtml += `
                        <tr class="border-b border-gray-600 ${bgColor}" id="row-${purchase.id}">
                            <td class="px-4 py-1">
                                <div id="display-${purchase.id}">
                                    <span class="cursor-pointer text-blue-400 hover:text-blue-300" onclick="enterEditMode('${purchase.id}')">
                                        ${purchase.item_name}
                                    </span>
                                    ${
                                      purchase.kodeitem
                                        ? `<div class="text-sm text-gray-400">${purchase.kodeitem}</div>`
                                        : ''
                                    }
                                </div>
                                <div id="edit-${purchase.id}" class="hidden">
                                    <input type="text" id="edit-name-${purchase.id}" value="${purchase.item_name}" class="bg-gray-700 text-white px-2 py-1 rounded w-full mb-1">
                                    ${
                                      purchase.kodeitem
                                        ? `<div class="text-sm text-gray-400">${purchase.kodeitem}</div>`
                                        : ''
                                    }
                                </div>
                            </td>
                            <td class="px-4 py-1">
                                <div id="qty-display-${purchase.id}">${purchase.quantity}</div>
                                <div id="qty-edit-${purchase.id}" class="hidden">
                                    <input type="number" id="edit-qty-${purchase.id}" value="${purchase.quantity}" class="bg-gray-700 text-white px-2 py-1 rounded w-full" step="1">
                                </div>
                            </td>
                            <td class="px-4 py-1">
                                <div id="date-display-${purchase.id}">${purchase.purchase_date}</div>
                                <div id="date-edit-${purchase.id}" class="hidden">
                                    <input type="date" id="edit-date-${purchase.id}" value="${convertDateForInput(purchase.purchase_date)}" class="bg-gray-700 text-white px-2 py-1 rounded">
                                </div>
                            </td>
                            <td class="px-4 py-1">
                                <label class="flex items-center">
                                    <input type="checkbox" ${
                                      purchase.received ? "checked" : ""
                                    } onchange="updateReceivedStatus('${
              purchase.id
            }', this.checked)" class="mr-2">
                                    <span class="${
                                      purchase.received
                                        ? "text-green-400"
                                        : "text-gray-400"
                                    }">${
              purchase.received ? "Yes" : "No"
            }</span>
                                </label>
                            </td>
                            <td class="px-4 py-1">
                                <div id="actions-display-${purchase.id}">
                                    <button onclick="deletePurchase('${
                                      purchase.id
                                    }')" class="text-red-400 hover:text-red-300">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <div id="actions-edit-${purchase.id}" class="hidden">
                                    <button onclick="savePurchaseEdit('${purchase.id}')" class="text-green-400 hover:text-green-300 mr-2">
                                        <i class="fas fa-save"></i>
                                    </button>
                                    <button onclick="cancelPurchaseEdit('${purchase.id}')" class="text-gray-400 hover:text-gray-300">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
          });

          dateGroupIndex++;
        });

        tbody.innerHTML = purchasesHtml;
      }

      async function updateReceivedStatus(purchaseId, received) {
        try {
          const response = await fetch(
            `/api/marketplace-purchases/${purchaseId}/received`,
            {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({ received }),
            }
          );

          const result = await response.json();
          if (result.success) {
            showNotification("Status updated", "success");
            loadPastPurchases();
          } else {
            showNotification("Failed to update status", "error");
          }
        } catch (error) {
          console.error("Update error:", error);
          showNotification("Failed to update status", "error");
        }
      }

      async function deletePurchase(purchaseId) {
        try {
          const response = await fetch(
            `/api/marketplace-purchases/${purchaseId}`,
            {
              method: "DELETE",
            }
          );

          const result = await response.json();
          if (result.success) {
            showNotification("Purchase deleted", "success");
            loadPastPurchases();
          } else {
            showNotification("Failed to delete purchase", "error");
          }
        } catch (error) {
          console.error("Delete error:", error);
          showNotification("Failed to delete purchase", "error");
        }
      }

      function convertDateForInput(dateStr) {
        // Convert "13-jun-2025" format to "2025-06-13" format for HTML date input
        if (!dateStr || dateStr.length === 0) return '';
        
        const parts = dateStr.split('-');
        if (parts.length !== 3) return dateStr; // Return as-is if not in expected format
        
        const day = parts[0].padStart(2, '0');
        const monthStr = parts[1].toLowerCase();
        const year = parts[2];
        
        const months = {
          'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04',
          'may': '05', 'jun': '06', 'jul': '07', 'aug': '08',
          'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12'
        };
        
        const month = months[monthStr] || '01';
        return `${year}-${month}-${day}`;
      }

      function enterEditMode(purchaseId) {
        // Hide display elements
        document.getElementById(`display-${purchaseId}`).classList.add('hidden');
        document.getElementById(`qty-display-${purchaseId}`).classList.add('hidden');
        document.getElementById(`date-display-${purchaseId}`).classList.add('hidden');
        document.getElementById(`actions-display-${purchaseId}`).classList.add('hidden');
        
        // Show edit elements
        document.getElementById(`edit-${purchaseId}`).classList.remove('hidden');
        document.getElementById(`qty-edit-${purchaseId}`).classList.remove('hidden');
        document.getElementById(`date-edit-${purchaseId}`).classList.remove('hidden');
        document.getElementById(`actions-edit-${purchaseId}`).classList.remove('hidden');
        
        // Focus on the name input
        document.getElementById(`edit-name-${purchaseId}`).focus();
      }

      function cancelPurchaseEdit(purchaseId) {
        // Show display elements
        document.getElementById(`display-${purchaseId}`).classList.remove('hidden');
        document.getElementById(`qty-display-${purchaseId}`).classList.remove('hidden');
        document.getElementById(`date-display-${purchaseId}`).classList.remove('hidden');
        document.getElementById(`actions-display-${purchaseId}`).classList.remove('hidden');
        
        // Hide edit elements
        document.getElementById(`edit-${purchaseId}`).classList.add('hidden');
        document.getElementById(`qty-edit-${purchaseId}`).classList.add('hidden');
        document.getElementById(`date-edit-${purchaseId}`).classList.add('hidden');
        document.getElementById(`actions-edit-${purchaseId}`).classList.add('hidden');
      }

      async function savePurchaseEdit(purchaseId) {
        try {
          const itemName = document.getElementById(`edit-name-${purchaseId}`).value.trim();
          const quantity = document.getElementById(`edit-qty-${purchaseId}`).value;
          const purchaseDate = document.getElementById(`edit-date-${purchaseId}`).value;
          
          if (!itemName || !quantity || !purchaseDate) {
            showNotification('Please fill in all fields', 'error');
            return;
          }
          
          if (parseFloat(quantity) <= 0) {
            showNotification('Quantity must be greater than 0', 'error');
            return;
          }
          
          const response = await fetch(`/api/marketplace-purchases/${purchaseId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              item_name: itemName,
              quantity: parseFloat(quantity),
              purchase_date: purchaseDate
            })
          });
          
          const result = await response.json();
          if (result.success) {
            showNotification('Purchase updated successfully', 'success');
            loadPastPurchases(); // Reload the table
          } else {
            showNotification('Failed to update purchase', 'error');
          }
        } catch (error) {
          console.error('Update error:', error);
          showNotification('Failed to update purchase', 'error');
        }
      }

      function showLoading(show) {
        const overlay = document.getElementById("loadingOverlay");
        if (show) {
          overlay.classList.remove("hidden");
        } else {
          overlay.classList.add("hidden");
        }
      }

      function showNotification(message, type = "info") {
        const notification = document.createElement("div");
        notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg z-50 ${
          type === "success"
            ? "bg-green-600"
            : type === "error"
            ? "bg-red-600"
            : "bg-blue-600"
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
          notification.remove();
        }, 3000);
      }

      function goBack() {
        window.history.back();
      }

      // Hide search results when clicking outside
      document.addEventListener("click", function (event) {
        if (
          !event.target.closest("#itemSearch") &&
          !event.target.closest("#searchResults")
        ) {
          hideSearchResults();
        }
      });
    </script>
  </body>
</html>
