<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login - Toko LARIS LAX</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            backgroundColor: {
              "gray-750": "#2d3748",
              "gray-850": "#1a1a1a",
              "gray-880": "#242424",
              "gray-900": "#1e293b",
            },
          },
        },
      };
    </script>
</head>
<body class="bg-gray-850 text-white min-h-screen flex items-center justify-center">
    <div class="w-full max-w-md p-8 space-y-8 bg-gray-880 rounded-lg shadow-lg">
        <div class="text-center">
            <h1 class="text-3xl font-bold">Toko LARIS LAX</h1>
            <p class="mt-2 text-gray-400">Please login to continue</p>
        </div>
        
        <form class="mt-8 space-y-6" action="/token" method="post">
            <div>
                <label for="username" class="block text-sm font-medium text-gray-300">Username</label>
                <input id="username" name="username" type="text" required 
                    class="mt-1 block w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white 
                    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            
            <div>
                <label for="password" class="block text-sm font-medium text-gray-300">Password</label>
                <input id="password" name="password" type="password" required 
                    class="mt-1 block w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white 
                    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            
            <div>
                <button type="submit" 
                    class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md 
                    shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 
                    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Sign in
                </button>
            </div>
        </form>
        
        <!-- WOL Toko Section -->
        <div class="mt-6 pt-6 border-t border-gray-600">
            <div class="text-center">
                <h3 class="text-lg font-medium mb-2 text-gray-300">System Utilities</h3>
                <p class="text-sm text-gray-400 mb-4">Wake On LAN for Toko system</p>
                <button 
                    id="wolTokoBtn"
                    onclick="openWolTokoPage()"
                    class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-150"
                >
                    Run WOL Toko
                </button>
            </div>
        </div>
    </div>

    <script>
        function openWolTokoPage() {
            // Open the WOL Toko output page in a new window/tab
            window.open('/wol-toko-page', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        }
    </script>
</body>
</html>
