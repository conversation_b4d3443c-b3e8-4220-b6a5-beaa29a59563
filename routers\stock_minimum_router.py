from fastapi import APIRouter, Request, Depends, HTTPException, Form
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from database.database_connection import get_db
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, or_
from database.database_model import (
    Item,
    ItemStock,
    ItemSellingPrice
)
import json

router = APIRouter()
templates = Jinja2Templates(directory="templates")

# Add currency filter
def currency_filter(value):
    try:
        return f"Rp {int(value):,}"
    except (ValueError, TypeError):
        return "Rp 0"

# Register the currency filter
templates.env.filters["currency"] = currency_filter

@router.get("/stock-minimum-management", response_class=HTMLResponse)
async def stock_minimum_management(request: Request, jenis: str = None, db: Session = Depends(get_db)):
    """Display items with stock minimum = 0 with filtering and editing capabilities"""
    
    # Base query for items with stock minimum = 0
    query = db.query(
        Item.kodeitem,
        Item.namaitem,
        Item.merek,
        Item.jenis,
        Item.stokmin,
        ItemSellingPrice.hargajual,
        ItemStock.stok
    ).outerjoin(
        ItemSellingPrice, 
        and_(
            Item.kodeitem == ItemSellingPrice.kodeitem,
            ItemSellingPrice.satuan == Item.satuan
        )
    ).outerjoin(
        ItemStock,
        Item.kodeitem == ItemStock.kodeitem
    ).filter(
        or_(Item.stokmin == 0, Item.stokmin.is_(None))
    )
    
    # Apply jenis filter if provided
    if jenis and jenis != "All":
        query = query.filter(Item.jenis == jenis)
    
    # Execute query and get results
    items = query.all()
    
    # Get unique jenis values for filter dropdown
    jenis_list = db.query(Item.jenis).filter(
        Item.jenis.isnot(None),
        Item.jenis != ""
    ).distinct().order_by(Item.jenis).all()
    jenis_options = [j[0] for j in jenis_list if j[0]]
    
    # Format items for template
    formatted_items = []
    for item in items:
        formatted_items.append({
            'kodeitem': item.kodeitem,
            'namaitem': item.namaitem or '',
            'merek': item.merek or 'None',
            'jenis': item.jenis or '',
            'stokmin': float(item.stokmin) if item.stokmin else 0.0,
            'hargajual': float(item.hargajual) if item.hargajual else 0.0,
            'stok': float(item.stok) if item.stok else 0.0
        })
    
    return templates.TemplateResponse("stock_minimum_management.html", {
        "request": request,
        "items": formatted_items,
        "jenis_options": jenis_options,
        "selected_jenis": jenis or "All",
        "items_count": len(formatted_items)
    })

@router.post("/update-stock-minimum")
async def update_stock_minimum(
    kodeitem: str = Form(...),
    stokmin: float = Form(...),
    db: Session = Depends(get_db)
):
    """Update stock minimum for a specific item"""
    
    try:
        # Find the item
        item = db.query(Item).filter(Item.kodeitem == kodeitem).first()
        
        if not item:
            raise HTTPException(status_code=404, detail="Item not found")
        
        # Update stock minimum
        item.stokmin = stokmin
        db.commit()
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"Stock minimum updated successfully for {kodeitem}",
                "kodeitem": kodeitem,
                "new_stokmin": stokmin
            }
        )
        
    except Exception as e:
        db.rollback()
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"Error updating stock minimum: {str(e)}"
            }
        )

@router.get("/check-item-price")
async def check_item_price(kodeitem: str, db: Session = Depends(get_db)):
    """Get item price for display purposes"""
    
    try:
        # Query item with price
        result = db.query(
            Item.kodeitem,
            Item.namaitem,
            ItemSellingPrice.hargajual
        ).outerjoin(
            ItemSellingPrice,
            and_(
                Item.kodeitem == ItemSellingPrice.kodeitem,
                ItemSellingPrice.satuan == Item.satuan
            )
        ).filter(Item.kodeitem == kodeitem).first()
        
        if not result:
            return JSONResponse(
                status_code=404,
                content={"success": False, "message": "Item not found"}
            )
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "kodeitem": result.kodeitem,
                "namaitem": result.namaitem,
                "hargajual": float(result.hargajual) if result.hargajual else 0.0
            }
        )
        
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"Error checking item price: {str(e)}"
            }
        )