from fastapi import APIRouter, Depends, Request, HTTPException
from sqlalchemy import func, and_, or_
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from typing import Dict, Optional
import pandas as pd

from database.database_connection import get_db
from database.database_model import (
    Item, 
    ItemStock, 
    ItemUnitConversion, 
    ItemSellingPrice, 
    ItemSalesHeader, 
    ItemSalesDetail
)
from fastapi.templating import Jinja2Templates

#dont delete this note: needed is calculated: Stock Needed = ((Yearly Sales / 12) * 3) - Current Stock

router = APIRouter()
templates = Jinja2Templates(directory="templates")

search_history: Dict[str, tuple[pd.DataFrame, str]] = {}

@router.get("/three-month-sales")
async def three_month_sales(
    request: Request,
    search: Optional[str] = None,
    distinct: Optional[str] = None,
    db: Session = Depends(get_db)
):
    items_data = []
    total_rows = 0
    total_items = 0
    
    if search:
        # Split search terms and remove empty strings
        search_terms = [term.strip() for term in search.split() if term.strip()]
        
        # Base query
        query = db.query(
            Item,
            ItemStock.stok,
            ItemUnitConversion.satuan,
            ItemUnitConversion.tipe,
            ItemUnitConversion.jumlahkonv,  # Make sure this is selected
            ItemUnitConversion.hargapokok,
            ItemSellingPrice.hargajual
        ).join(
            ItemStock, 
            (Item.kodeitem == ItemStock.kodeitem)
        ).join(
            ItemUnitConversion,
            (Item.kodeitem == ItemUnitConversion.kodeitem)
        ).outerjoin(
            ItemSellingPrice,
            (Item.kodeitem == ItemSellingPrice.kodeitem) &
            (ItemUnitConversion.satuan == ItemSellingPrice.satuan)
        )

        # Apply search filters
        for term in search_terms:
            query = query.filter(
                func.lower(
                    Item.kodeitem + ' ' +  # Add spaces between concatenated fields
                    Item.namaitem + ' ' + 
                    func.coalesce(Item.merek, '') + ' ' +  # Handle NULL values
                    func.coalesce(Item.jenis, '') + ' ' + 
                    func.coalesce(Item.keterangan, '') + ' ' + 
                    func.coalesce(Item.supplier1, '')
                ).contains(term.lower())
            )

        results = query.all()

        # Get current date for calculations
        current_date = datetime.now()
        one_year_ago = current_date - timedelta(days=365)

        # Calculate monthly sales for each item
        monthly_sales = {}
        for result in results:
            kodeitem = result.Item.kodeitem
            monthly_data = []
            has_sales = False
            
            # Get current month
            current_month = current_date.month
            
            for i in range(3):
                # Calculate month and year for last year's corresponding months
                month = (current_month + i) % 12 or 12  # Convert 0 to 12
                year = current_date.year - 1  # Last year
                
                # Create start and end dates for the month
                start_date = datetime(year, month, 1)
                if month == 12:
                    end_date = datetime(year + 1, 1, 1)
                else:
                    end_date = datetime(year, month + 1, 1)
                
                # Query sales with unit conversion information
                sales_with_units = db.query(
                    ItemSalesDetail.jumlah,
                    ItemSalesDetail.satuan
                ).join(
                    ItemSalesHeader,
                    (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
                    (ItemSalesHeader.tipe == 'KSR')
                ).filter(
                    ItemSalesDetail.kodeitem == kodeitem,
                    ItemSalesHeader.tanggal >= start_date,
                    ItemSalesHeader.tanggal < end_date
                ).all()
                
                # Calculate total sales with unit conversion
                month_sales = 0
                for sale in sales_with_units:
                    # Get conversion factor for this unit
                    conversion = db.query(ItemUnitConversion.jumlahkonv, ItemUnitConversion.tipe)\
                        .filter(
                            ItemUnitConversion.kodeitem == kodeitem,
                            ItemUnitConversion.satuan == sale.satuan
                        ).first()
                    
                    if conversion:
                        if conversion.tipe == 'K':  # Conversion unit
                            month_sales += sale.jumlah * conversion.jumlahkonv
                        else:  # Base unit
                            month_sales += sale.jumlah
                    else:
                        # If no conversion found, use as is
                        month_sales += sale.jumlah
                
                if month_sales > 0:
                    has_sales = True
                monthly_data.append(str(int(month_sales)))
            
            if has_sales:
                monthly_sales[kodeitem] = '-'.join(monthly_data)  # Will show Mar-Apr-May
            else:
                monthly_sales[kodeitem] = ''

        # Calculate yearly sales for stock needed calculation with unit conversion
        sales_dict = {}
        
        # Process each item individually to handle unit conversions
        for result in results:
            kodeitem = result.Item.kodeitem
            
            # Get all sales for this item in the past year
            sales_with_units = db.query(
                ItemSalesDetail.jumlah,
                ItemSalesDetail.satuan
            ).join(
                ItemSalesHeader,
                (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
                (ItemSalesHeader.tipe == 'KSR')
            ).filter(
                ItemSalesDetail.kodeitem == kodeitem,
                ItemSalesHeader.tanggal >= one_year_ago
            ).all()
            
            # Calculate total sales with unit conversion
            yearly_sale = 0
            for sale in sales_with_units:
                # Get conversion factor for this unit
                conversion = db.query(ItemUnitConversion.jumlahkonv, ItemUnitConversion.tipe)\
                    .filter(
                        ItemUnitConversion.kodeitem == kodeitem,
                        ItemUnitConversion.satuan == sale.satuan
                    ).first()
                
                if conversion:
                    if conversion.tipe == 'K':  # Conversion unit
                        yearly_sale += sale.jumlah * conversion.jumlahkonv
                    else:  # Base unit
                        yearly_sale += sale.jumlah
                else:
                    # If no conversion found, use as is
                    yearly_sale += sale.jumlah
            
            sales_dict[kodeitem] = yearly_sale

        # Process results with proper unit conversion
        for result in results:
            kodeitem = result.Item.kodeitem
            
            # Calculate stock needed for 3 months
            yearly_sale = sales_dict.get(kodeitem, 0)
            three_month_need = (yearly_sale / 12) * 3
            
            # Get base stock (always in base unit)
            current_stock = result.stok if result.stok is not None else 0
            
            # Calculate if stock is low (always compare in base units)
            is_low_stock = current_stock <= result.Item.stokmin
            
            # Convert stock for display if needed
            converted_stock = current_stock
            if hasattr(result, 'tipe') and result.tipe == 'K' and hasattr(result, 'jumlahkonv') and result.jumlahkonv:
                converted_stock = current_stock / result.jumlahkonv
            
            needed = three_month_need - converted_stock
            stock_needed = round(needed, 2) if needed > 0 else None

            items_data.append({
                'item': result.Item,
                'stok': converted_stock,  # Display value in appropriate unit
                'base_stock': current_stock,  # Original stock in base unit
                'is_low_stock': is_low_stock,  # Flag based on base unit comparison
                'last_3months_sales': monthly_sales.get(kodeitem, ''),
                'stock_needed_3month': stock_needed,
                'hargapokok': result.hargapokok,
                'hargajual': result.hargajual,
                'satuan': result.satuan,
                'tipe': result.tipe
            })

        # Apply distinct filter if enabled
        is_distinct = distinct == 'true'  # This will be False if distinct is None or 'false'
        if is_distinct:
            # Create a dictionary to store unique items by kodeitem
            unique_items = {}
            for item in items_data:
                kodeitem = item['item'].kodeitem
                if kodeitem not in unique_items:
                    unique_items[kodeitem] = item
            items_data = list(unique_items.values())

        # Calculate counts after filtering
        total_rows = len(items_data)
        total_items = len(set(item['item'].kodeitem for item in items_data))

    return templates.TemplateResponse(
        "three_month_sales.html",
        {
            "request": request,
            "items": items_data,
            "search": search,
            "total_rows": total_rows,
            "total_items": total_items,
            "distinct": is_distinct if search else False  # Default to False if no search
        }
    )