from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from routers import (
    auth_router,
    landing_page_router, 
    today_sales_router, 
    item_history_router, 
    edit_item_router, 
    monthly_profit_router,
    monthly_profit_by_category_router,
    order_items_router, 
    export_router,
    discounted_items_router,
    daily_profit_router,
    low_stock_items_router,
    monthly_low_stock_items_router,
    three_month_sales_router,
    twelve_month_sales_router,
    chat_history_router,
    utilities_router,
    marketplace_purchases_router,
    items_not_sold_by_time_router,
    stock_minimum_router
)
# from middleware import auth_middleware  # Disabled authentication

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add middleware
# app.middleware("http")(auth_middleware)  # Disabled authentication

# Mount the static directory to serve CSS files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Set up Jinja2 templates
templates = Jinja2Templates(directory="templates")

# Include the routers
app.include_router(auth_router.router)
app.include_router(landing_page_router.router)
app.include_router(today_sales_router.router)
app.include_router(item_history_router.router)
app.include_router(edit_item_router.router)
app.include_router(monthly_profit_router.router)
app.include_router(monthly_profit_by_category_router.router)
app.include_router(order_items_router.router)
app.include_router(export_router.router)
app.include_router(discounted_items_router.router)
app.include_router(daily_profit_router.router)
app.include_router(low_stock_items_router.router)
app.include_router(monthly_low_stock_items_router.router)
app.include_router(three_month_sales_router.router)
app.include_router(twelve_month_sales_router.router)
app.include_router(chat_history_router.router)
app.include_router(utilities_router.router)
app.include_router(marketplace_purchases_router.router)
app.include_router(items_not_sold_by_time_router.router)
app.include_router(stock_minimum_router.router)

# Optional: Add a root redirect
@app.get("/")
async def root():
    return {"message": "API is running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",  # Listen on all network interfaces
        port=7777,       # Choose your desired port
        reload=True      # Optional: Enable auto-reload during development
    )
