<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Profit by Category</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            darkMode: "class",
            theme: {
                extend: {
                    backgroundColor: {
                        "gray-750": "#2d3748",
                        "gray-850": "#1a1a1a",
                        "gray-880": "#242424",
                        "gray-900": "#1e293b",
                    },
                },
            },
        };
    </script>
</head>
<body class="bg-black text-gray-100">
    <div class="min-h-screen">
        <!-- Navbar -->
        <nav class="bg-gray-800 shadow-lg sticky top-0 z-50">
            <div class="max-w-[95%] mx-auto px-4">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-3xl font-bold text-white">Monthly Profit by Category</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button
                            onclick="window.close()"
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center"
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="max-w-[95%] mx-auto px-4 py-6">
            <!-- Month/Year Selector -->
            <div class="bg-gray-800 rounded-lg p-6 mb-6">
                <h2 class="text-2xl font-bold mb-4">Select Period</h2>
                <div class="flex flex-wrap gap-4 items-end">
                    <div class="flex flex-col">
                        <label for="monthSelect" class="text-sm font-medium text-gray-300 mb-2">Month</label>
                        <select id="monthSelect" class="bg-gray-700 text-white px-4 py-2 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none">
                            {% for month in months %}
                            <option value="{{ month.value }}" {% if month.value == selected_month %}selected{% endif %}>
                                {{ month.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="flex flex-col">
                        <label for="yearSelect" class="text-sm font-medium text-gray-300 mb-2">Year</label>
                        <select id="yearSelect" class="bg-gray-700 text-white px-4 py-2 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none">
                            {% for year in years %}
                            <option value="{{ year }}" {% if year == selected_year %}selected{% endif %}>
                                {{ year }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <button 
                        onclick="updateReport()" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-150"
                    >
                        Update Report
                    </button>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-xl font-bold mb-2 text-gray-300">Total Gross Sales</h3>
                    <p class="text-3xl font-bold text-blue-400">{{ total_gross_sales | currency }}</p>
                </div>
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-xl font-bold mb-2 text-gray-300">Total Net Profit</h3>
                    <p class="text-3xl font-bold text-green-400">{{ total_net_profit | currency }}</p>
                </div>
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-xl font-bold mb-2 text-gray-300">Total Categories</h3>
                    <p class="text-3xl font-bold text-yellow-400">{{ grouped_profits|length }}</p>
                </div>
            </div>

            <!-- Current Period Display -->
            <div class="bg-gray-800 rounded-lg p-6 mb-6">
                <h2 class="text-2xl font-bold mb-4">{{ month_name }} {{ selected_year }} - Profit by Category</h2>
                
                <!-- Chart Container -->
                <div class="mb-6">
                    <canvas id="profitChart" width="400" height="200"></canvas>
                </div>

                <!-- Category Details -->
                <div class="space-y-4">
                    {% for group_name, group_data in grouped_profits %}
                    <div class="bg-gray-700 rounded-lg p-4">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-xl font-bold text-white">{{ group_name }}</h3>
                            <div class="flex flex-col items-end">
                                <span class="text-lg font-bold text-gray-300">{{ group_data.gross_sales | currency }}</span>
                                <span class="text-lg font-bold text-green-400">{{ group_data.net_profit | currency }}</span>
                                <span class="text-lg font-bold text-yellow-400">{{ "%.1f"|format(group_data.contribution_percentage) }}%</span>
                            </div>
                        </div>
                        
                        <!-- Show individual categories if it's a group -->
                        {% if group_data.categories|length > 1 %}
                        <div class="ml-4 space-y-2">
                            {% for category in group_data.categories %}
                            <div class="flex justify-between items-center text-sm bg-gray-600 rounded p-2">
                                <span class="text-gray-300">{{ category.name }}</span>
                                <div class="flex flex-col items-end">
                                    <span class="text-gray-400">{{ category.gross_sales | currency }}</span>
                                    <span class="text-green-300">{{ category.net_profit | currency }}</span>
                                    <span class="text-yellow-300">{{ "%.1f"|format(category.total_contribution_percentage) }}%</span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <script>
        // Chart.js configuration
        const ctx = document.getElementById('profitChart').getContext('2d');
        const chartData = {
            labels: [
                {% for group_name, group_data in grouped_profits %}
                '{{ group_name }}'{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            datasets: [{
                label: 'Sales Contribution (%)',
                data: [
                    {% for group_name, group_data in grouped_profits %}
                    {{ "%.1f"|format(group_data.contribution_percentage) }}{% if not loop.last %},{% endif %}
                    {% endfor %}
                ],
                backgroundColor: [
                    '#10B981', '#3B82F6', '#8B5CF6', '#F59E0B', '#EF4444', 
                    '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
                ],
                borderColor: '#374151',
                borderWidth: 1
            }]
        };

        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: chartData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#D1D5DB',
                            padding: 20
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                return context.label + ': ' + value + '%';
                            }
                        }
                    }
                }
            }
        });

        function updateReport() {
            const month = document.getElementById('monthSelect').value;
            const year = document.getElementById('yearSelect').value;
            window.location.href = `/monthly-profit-by-category?month=${month}&year=${year}`;
        }
    </script>
</body>
</html>