from sqlalchemy import Column, Integer, String, DateTime, Float, ForeignKey, Numeric
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

class Item(Base):
    __tablename__ = 'tbl_item'
    
    kodeitem = Column(String, primary_key=True)
    namaitem = Column(String)
    jenis = Column(String)
    stokmin = Column(Numeric(18, 3))  # Fixed: Changed from Integer to Numeric to match database
    rak = Column(String)
    satuan = Column(String)  # unit dasar D
    keterangan = Column(String)  
    supplier1 = Column(String)
    merek = Column(String)

class ItemSalesDetail(Base):
    __tablename__ = 'tbl_ikdt'
    
    iddetail = Column(String, primary_key=True)  # Fixed: Changed from Integer to String
    nobaris = Column(Integer)
    notransaksi = Column(String, ForeignKey('tbl_ikhd.notransaksi'))
    kodeitem = Column(String, ForeignKey('tbl_item.kodeitem'))
    jumlah = Column(Float)
    satuan = Column(String)
    harga = Column(Numeric(18, 2))
    potongan = Column(Numeric(18, 2))
    total = Column(Numeric(18, 2))

class ItemSalesHeader(Base):
    __tablename__ = 'tbl_ikhd'
    
    notransaksi = Column(String, primary_key=True)
    tanggal = Column(DateTime)
    tipe = Column(String)  # KSR=Item Sold, RJ=Item Return, IK=Item keluar, JL=item jual
    kodesupel = Column(String)
    keterangan = Column(String)
    totalitem = Column(Float)
    subtotal = Column(Numeric(18, 2))
    potfaktur = Column(Numeric(18, 2))
    biayalain = Column(Numeric(18, 2))
    totalakhir = Column(Numeric(18, 2))
    potnomfaktur = Column(Numeric(18, 2))
    
    details = relationship("ItemSalesDetail", backref="header")

class ItemPurchaseDetail(Base):
    __tablename__ = 'tbl_imdt'
    
    iddetail = Column(String, primary_key=True)  # Fixed: Changed from Integer to String
    nobaris = Column(Integer)
    notransaksi = Column(String, ForeignKey('tbl_imhd.notransaksi'))
    kodeitem = Column(String, ForeignKey('tbl_item.kodeitem'))
    jumlah = Column(Float)
    satuan = Column(String)
    harga = Column(Numeric(18, 2))
    potongan = Column(Numeric(18, 2))
    total = Column(Numeric(18, 2))

class ItemPurchaseHeader(Base):
    __tablename__ = 'tbl_imhd'
    
    notransaksi = Column(String, primary_key=True)
    tanggal = Column(DateTime)
    tipe = Column(String)  # BL=item beli, IM=item masuk
    kodesupel = Column(String)
    keterangan = Column(String)
    totalitem = Column(Float)
    subtotal = Column(Numeric(18, 2))
    potfaktur = Column(Numeric(18, 2))
    biayalain = Column(Numeric(18, 2))
    totalakhir = Column(Numeric(18, 2))
    potnomfaktur = Column(Numeric(18, 2))
    
    details = relationship("ItemPurchaseDetail", backref="header")

class ItemSellingPrice(Base):
    __tablename__ = 'tbl_itemhj'
    
    iddetail = Column(String, primary_key=True)
    kodeitem = Column(String, ForeignKey('tbl_item.kodeitem'))
    prosentase = Column(Float)
    satuan = Column(String)
    hargajual = Column(Numeric(18, 2))

class ItemUnitConversion(Base):
    __tablename__ = 'tbl_itemsatuanjml'
    
    iddetail = Column(String, primary_key=True)
    kodeitem = Column(String, ForeignKey('tbl_item.kodeitem'))
    satuan = Column(String)
    jumlahkonv = Column(Float)  # conversion amount
    kodebarcode = Column(String)
    hargapokok = Column(Numeric(18, 2))
    tipe = Column(String)  # D=unit dasar, K=unit konversi

class ItemStock(Base):
    __tablename__ = 'tbl_itemstok'
    
    kodeitem = Column(String, ForeignKey('tbl_item.kodeitem'), primary_key=True)
    kantor = Column(String, primary_key=True)
    stok = Column(Float)  # always in unit dasar (D)

class ItemStockOpname(Base):
    __tablename__ = 'tbl_itemopname'
    
    iddetail = Column(String, primary_key=True)  # Changed to String to match your format
    periode = Column(String)
    tanggal = Column(DateTime)
    kodeitem = Column(String, ForeignKey('tbl_item.kodeitem'))
    kodekantor = Column(String)  # Added
    satuan = Column(String)
    jmlsebelum = Column(Float)
    jmlfisik = Column(Float)
    jmlselisih = Column(Float)
    kodeacc = Column(String)  # Added
    user1 = Column(String)  # Added
    user2 = Column(String, nullable=True)  # Added, nullable
    dateupd = Column(DateTime)  # Added
    harga = Column(Numeric(18, 2))
    total = Column(Numeric(18, 2))
    compname = Column(String)  # Added

class TransactionFormat(Base):
    __tablename__ = 'tbl_formatnotr'
    
    trid = Column(Integer, primary_key=True)
    nomor = Column(Integer)
    notransaksi = Column(String)
