// Chat history popup functionality
function searchChatHistory(itemName, itemCode) {
  try {
    // Show loading toast
    // Toastify({
    //   text: "Loading chat history...",
    //   duration: 3000,
    //   gravity: "top",
    //   position: "right",
    //   style: {
    //     background: "#3b82f6", // blue-500
    //   },
    // }).showToast();

    fetch(`/search-chat-history?item=${encodeURIComponent(itemCode)}`)
      .then((response) => {
        if (!response.ok) {
          throw new Error("Failed to search chat history");
        }
        return response.json();
      })
      .then((data) => {
        // Get chat history from response
        let chatHistory = data.chat_history || [];

        // Sort by date (newest first)
        chatHistory.sort((a, b) => new Date(b.date) - new Date(a.date));

        // Create the popup with the chat history
        createChatHistoryPopup(itemName, chatHistory);
      })
      .catch((error) => {
        console.error("Error searching chat history:", error);
        Toastify({
          text: "Failed to search chat history: " + error.message,
          duration: 3000,
          gravity: "bottom",
          position: "center",
          style: {
            background: "#ef4444", // red-600
          },
        }).showToast();
      });
  } catch (error) {
    console.error("Error searching chat history:", error);
    Toastify({
      text: "Failed to search chat history: " + error.message,
      duration: 3000,
      gravity: "bottom",
      position: "center",
      style: {
        background: "#ef4444", // red-600
      },
    }).showToast();
  }
}

function createChatHistoryPopup(itemName, chatHistory) {
  // Create popup
  const popup = document.createElement("div");
  popup.className =
    "fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50";

  // Create content
  let content = `
    <div class="bg-gray-800 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-y-auto">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">Chat History for "${itemName}"</h2>
        <button class="text-gray-400 hover:text-white" id="closePopup">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      <div class="mb-4 flex space-x-2">
        <button id="sendToTelegram" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
          Send to Telegram
        </button>
        <button id="sendToTelegramNoBuy" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
          Send to Telegram No BUY
        </button>
      </div>`;

  // Chat History Section
  content += `
    <div>
      <h3 class="text-lg font-semibold mb-2 text-blue-400">Item History</h3>`;

  if (chatHistory.length === 0) {
    content += `
      <div class="flex flex-col items-center justify-center py-4">
        <img src="/static/img/none.jpg" alt="No history found" class="max-w-full h-auto rounded-lg mb-2">
        <p class="text-gray-300">No history found for this item.</p>
      </div>`;
  } else {
    content += `
      <table class="w-full">
        <thead class="bg-gray-700">
          <tr>
            <th class="px-4 py-2 text-left">Date</th>
            <th class="px-4 py-2 text-left">Source</th>
            <th class="px-4 py-2 text-left">Details</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-700">`;

    chatHistory.forEach((item) => {
      // Format date to DD-MMM-YYYY
      const date = new Date(item.date);
      const formattedDate = `${date
        .getDate()
        .toString()
        .padStart(2, "0")}-${date.toLocaleString("en", {
        month: "short",
      })}-${date.getFullYear()}`;

      // Calculate days difference
      const today = new Date();
      const diffTime = Math.abs(today - date);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      // Determine message color based on age
      let messageClass = "text-white"; // Default color
      if (diffDays > 60) {
        messageClass = "text-red-400"; // Red for > 60 days
      } else if (diffDays > 30) {
        messageClass = "text-yellow-400"; // Yellow for > 30 days
      }

      // Check if this is a transaction entry (from purchase history)
      const isTransaction = item.user && item.user.includes("/UTM/");
      const strikeClass = isTransaction ? "line-through" : "";

      content += `
        <tr class="hover:bg-gray-700 ${messageClass.replace(
          "text-",
          "hover:"
        )}">
          <td class="px-4 py-2 ${messageClass} ${strikeClass}">${formattedDate}</td>
          <td class="px-4 py-2 ${messageClass} ${strikeClass}">${item.user}</td>
          <td class="px-4 py-2 ${messageClass} ${strikeClass}">${
        item.message
      }</td>
        </tr>`;
    });

    content += `
        </tbody>
      </table>`;
  }
  content += `</div>`;
  content += `</div>`;
  popup.innerHTML = content;

  // Add to body
  document.body.appendChild(popup);

  // Check for purchase history entries (transactions)
  const purchaseEntries = chatHistory.filter(
    (item) => item.user && item.user.includes("/UTM/")
  );

  // Check for chat history entries (non-transactions)
  const chatEntries = chatHistory.filter(
    (item) => !item.user || !item.user.includes("/UTM/")
  );

  // Get the newest purchase date if any
  let newestPurchaseDate = null;
  if (purchaseEntries.length > 0) {
    newestPurchaseDate = new Date(
      Math.max(...purchaseEntries.map((item) => new Date(item.date)))
    );
  }

  // Get the newest chat date if any
  let newestChatDate = null;
  if (chatEntries.length > 0) {
    newestChatDate = new Date(
      Math.max(...chatEntries.map((item) => new Date(item.date)))
    );
  }

  // Set up today's date for calculations
  const today = new Date();

  // Check if there's a chat entry within the last 30 days
  const hasRecentChat = chatEntries.some((item) => {
    const date = new Date(item.date);
    const diffTime = Math.abs(today - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 30;
  });

  // Only play sound if:
  // 1. There are recent chat entries (less than 30 days old)
  // 2. AND chat history is newer than purchase history (if purchase history exists)
  const shouldPlaySound =
    hasRecentChat &&
    (!newestPurchaseDate ||
      !newestChatDate ||
      newestChatDate > newestPurchaseDate);

  if (shouldPlaySound) {
    const audio = new Audio("/static/sound/ding.wav");
    audio.play().catch((err) => console.error("Error playing sound:", err));
  }

  // Add close functionality
  document.getElementById("closePopup").addEventListener("click", () => {
    document.body.removeChild(popup);
  });

  // Add send to Telegram functionality
  document
    .getElementById("sendToTelegram")
    .addEventListener("click", async () => {
      try {
        // Replace escaped quotes with actual quotes to preserve formatting
        const processedItemName = itemName.replace(/\\"|\"/g, '"');
        
        const sendResponse = await fetch("/send-to-telegram", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ message: processedItemName }),
        });

        const result = await sendResponse.json();

        if (result.success) {
          Toastify({
            text: "Message sent to Telegram",
            duration: 3000,
            gravity: "bottom",
            position: "center",
            style: {
              background: "#22c55e", // green-600
            },
          }).showToast();

          // Close the popup after successful send
          document.body.removeChild(popup);
        } else {
          throw new Error(result.error || "Failed to send message");
        }
      } catch (error) {
        console.error("Error sending to Telegram:", error);
        Toastify({
          text: `Failed to send message: ${error.message}`,
          duration: 3000,
          gravity: "bottom",
          position: "center",
          style: {
            background: "#ef4444", // red-600
          },
        }).showToast();
      }
    });

  // Add send to Telegram No BUY functionality
  document
    .getElementById("sendToTelegramNoBuy")
    .addEventListener("click", async () => {
      try {
        // Replace escaped quotes with actual quotes to preserve formatting
        const processedItemName = itemName.replace(/\\"|\"/g, '"');
        
        const sendResponse = await fetch("/send-to-telegram", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ message: `${processedItemName} No BUY` }),
        });

        const result = await sendResponse.json();

        if (result.success) {
          Toastify({
            text: "Message sent to Telegram with No BUY",
            duration: 3000,
            gravity: "bottom",
            position: "center",
            style: {
              background: "#22c55e", // green-600
            },
          }).showToast();

          // Close the popup after successful send
          document.body.removeChild(popup);
        } else {
          throw new Error(result.error || "Failed to send message");
        }
      } catch (error) {
        console.error("Error sending to Telegram:", error);
        Toastify({
          text: `Failed to send message: ${error.message}`,
          duration: 3000,
          gravity: "bottom",
          position: "center",
          style: {
            background: "#ef4444", // red-600
          },
        }).showToast();
      }
    });

  // Close on click outside
  popup.addEventListener("click", (e) => {
    if (e.target === popup) {
      document.body.removeChild(popup);
    }
  });
}

function copyToClipboard(text) {
  console.log("Copying to clipboard:", text);

  // Try modern Clipboard API first
  if (navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        // Show a toast notification
        Toastify({
          text: "Product name copied!",
          duration: 2000,
          gravity: "bottom",
          position: "center",
          backgroundColor: "#4CAF50",
        }).showToast();
      })
      .catch((err) => {
        console.error("Clipboard API failed: ", err);
        fallbackCopy(text);
      });
  } else {
    // Fall back to older method
    fallbackCopy(text);
  }
}

// Fallback copy method using textarea
function fallbackCopy(text) {
  try {
    // Create a temporary textarea element
    const textarea = document.createElement("textarea");
    textarea.value = text;

    // Make the textarea out of viewport
    textarea.style.position = "fixed";
    textarea.style.left = "-999999px";
    textarea.style.top = "-999999px";

    document.body.appendChild(textarea);
    textarea.focus();
    textarea.select();

    // Execute copy command
    const successful = document.execCommand("copy");
    document.body.removeChild(textarea);

    if (successful) {
      Toastify({
        text: "Product name copied!",
        duration: 2000,
        gravity: "bottom",
        position: "center",
        backgroundColor: "#4CAF50",
      }).showToast();
    } else {
      throw new Error("Copy command was unsuccessful");
    }
  } catch (err) {
    console.error("Fallback copy failed: ", err);
    Toastify({
      text: "Failed to copy text",
      duration: 2000,
      gravity: "bottom",
      position: "center",
      backgroundColor: "#e74c3c",
    }).showToast();
  }
}

// Setup event listeners when DOM is loaded
function setupEventListeners() {
  console.log("DOM loaded, setting up click handlers");

  // Add click handlers to all product name elements
  document
    .querySelectorAll('[onclick*="searchChatHistory"]')
    .forEach((element) => {
      const match = element
        .getAttribute("onclick")
        .match(/searchChatHistory\('([^']+)',\s*'([^']+)'\)/);
      if (match && match[1] && match[2]) {
        const itemName = match[1];
        const itemCode = match[2];

        // Remove the inline onclick and add an event listener
        element.removeAttribute("onclick");
        element.addEventListener("click", function () {
          console.log("Element clicked, calling searchChatHistory");
          searchChatHistory(itemName, itemCode);
        });
      }
    });

  // Add click handlers for search item name elements
  document.querySelectorAll(".search-item-name").forEach((element) => {
    console.log("Found search-item-name element:", element);
    element.addEventListener("click", function () {
      const itemName = this.getAttribute("data-item-name");
      const itemCode = this.getAttribute("data-item-code");
      console.log("Item name clicked:", itemName, itemCode);
      searchChatHistory(itemName, itemCode);
    });
  });

  // Add click handlers for copy buttons
  document.querySelectorAll(".copy-button").forEach((element) => {
    console.log("Found copy-button element:", element);
    element.addEventListener("click", function (event) {
      event.stopPropagation(); // Prevent triggering parent click events
      const textToCopy = this.getAttribute("data-copy-text");
      console.log("Copy button clicked:", textToCopy);
      copyToClipboard(textToCopy);
    });
  });

  // Add direct console logging to help debug
  console.log(
    "Total search-item-name elements found:",
    document.querySelectorAll(".search-item-name").length
  );
  console.log(
    "Total copy-button elements found:",
    document.querySelectorAll(".copy-button").length
  );
}

// Make functions available globally
window.searchChatHistory = searchChatHistory;
window.copyToClipboard = copyToClipboard;

// Make setupEventListeners available globally but don't automatically attach it
// This allows individual pages to decide whether to use it or not
window.setupChatHistoryEventListeners = setupEventListeners;
